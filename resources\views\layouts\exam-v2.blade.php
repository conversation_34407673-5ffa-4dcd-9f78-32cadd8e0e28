<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Ujian - Ngambiskuy')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional Styles -->
    <style>
        /* Custom styles for exam interface */
        .exam-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .question-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .question-card:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .option-card {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .option-card:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        
        .option-card.selected {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        
        .timer-warning {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }
        
        /* Question navigation styles */
        .question-nav-btn {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .question-nav-btn.answered {
            background-color: #10b981;
            color: white;
            border: 2px solid #10b981;
        }
        
        .question-nav-btn.current {
            background-color: #3b82f6;
            color: white;
            border: 2px solid #3b82f6;
        }
        
        .question-nav-btn.unanswered {
            background-color: #f3f4f6;
            color: #6b7280;
            border: 2px solid #d1d5db;
        }
        
        .question-nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        /* Auto-save indicator */
        .auto-save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .auto-save-indicator.saving {
            background-color: #fbbf24;
            color: #92400e;
        }
        
        .auto-save-indicator.saved {
            background-color: #10b981;
            color: white;
        }
        
        .auto-save-indicator.error {
            background-color: #ef4444;
            color: white;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .question-nav-btn {
                width: 35px;
                height: 35px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body class="font-sans antialiased">
    <div class="exam-container">
        @yield('content')
    </div>

    <!-- Auto-save indicator -->
    <div id="autoSaveIndicator" class="auto-save-indicator" style="display: none;">
        <span id="autoSaveText">Menyimpan...</span>
    </div>

    <!-- Scripts -->
    <script>
        // Global exam utilities
        window.ExamUtils = {
            // Auto-save functionality
            autoSave: function(examId, questionId, data) {
                return new Promise((resolve, reject) => {
                    const indicator = document.getElementById('autoSaveIndicator');
                    const text = document.getElementById('autoSaveText');
                    
                    // Show saving indicator
                    indicator.className = 'auto-save-indicator saving';
                    indicator.style.display = 'block';
                    text.textContent = 'Menyimpan...';
                    
                    fetch(`/exams/${examId}/auto-save`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            question_id: questionId,
                            ...data
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success indicator
                            indicator.className = 'auto-save-indicator saved';
                            text.textContent = 'Tersimpan';
                            
                            // Hide after 2 seconds
                            setTimeout(() => {
                                indicator.style.display = 'none';
                            }, 2000);
                            
                            resolve(data);
                        } else {
                            throw new Error(data.message || 'Failed to save');
                        }
                    })
                    .catch(error => {
                        // Show error indicator
                        indicator.className = 'auto-save-indicator error';
                        text.textContent = 'Gagal menyimpan';
                        
                        // Hide after 3 seconds
                        setTimeout(() => {
                            indicator.style.display = 'none';
                        }, 3000);
                        
                        console.error('Auto-save error:', error);
                        reject(error);
                    });
                });
            },
            
            // Timer utilities
            formatTime: function(seconds) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;
                
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            },
            
            // Progress calculation
            calculateProgress: function(answered, total) {
                return total > 0 ? Math.round((answered / total) * 100) : 0;
            },
            
            // Question navigation
            updateQuestionStatus: function(questionIndex, isAnswered) {
                const btn = document.querySelector(`[data-question="${questionIndex}"]`);
                if (btn) {
                    btn.classList.remove('answered', 'unanswered');
                    btn.classList.add(isAnswered ? 'answered' : 'unanswered');
                }
            },
            
            // Confirmation dialogs
            confirmSubmit: function(unansweredCount) {
                if (unansweredCount > 0) {
                    return confirm(`Anda masih memiliki ${unansweredCount} soal yang belum dijawab. Apakah Anda yakin ingin menyelesaikan ujian?`);
                } else {
                    return confirm('Apakah Anda yakin ingin menyelesaikan ujian? Anda tidak dapat mengubah jawaban setelah ini.');
                }
            }
        };
        
        // Prevent accidental page refresh
        window.addEventListener('beforeunload', function(e) {
            e.preventDefault();
            e.returnValue = 'Anda yakin ingin meninggalkan halaman? Progress ujian mungkin akan hilang.';
        });
        
        // Disable right-click context menu during exam
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // Disable certain keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Disable F12, Ctrl+Shift+I, Ctrl+U, etc.
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });
    </script>
    
    @stack('scripts')
</body>
</html>
