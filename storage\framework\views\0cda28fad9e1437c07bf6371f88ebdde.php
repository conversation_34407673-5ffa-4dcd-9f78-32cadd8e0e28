<?php $__env->startSection('title', $course->title . ' - Ngambiskuy'); ?>

<?php $__env->startSection('content'); ?>
<div class="bg-gray-50 min-h-screen">
    <!-- Course Header -->
    <div class="bg-gradient-to-r from-primary to-secondary text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-3 gap-8">
                <!-- Course Info -->
                <div class="lg:col-span-2">
                    <!-- Breadcrumb -->
                    <nav class="mb-6">
                        <ol class="flex items-center space-x-2 text-sm opacity-80">
                            <li><a href="<?php echo e(route('home')); ?>" class="hover:text-white">Beranda</a></li>
                            <li><span class="mx-2">/</span></li>
                            <li><a href="<?php echo e(route('courses.index')); ?>" class="hover:text-white">Kurikulum</a></li>
                            <li><span class="mx-2">/</span></li>
                            <li><a href="<?php echo e(route('courses.index', ['category' => $course->category->slug])); ?>" class="hover:text-white"><?php echo e($course->category->name); ?></a></li>
                            <li><span class="mx-2">/</span></li>
                            <li class="text-white"><?php echo e($course->title); ?></li>
                        </ol>
                    </nav>

                    <h1 class="text-4xl md:text-5xl font-bold mb-4"><?php echo e($course->title); ?></h1>
                    <p class="text-xl mb-6 opacity-90" data-course-description><?php echo e($course->description); ?></p>

                    <!-- Course Meta -->
                    <div class="flex flex-wrap items-center gap-6 text-sm">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span><?php echo e($course->tutor->name); ?></span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span><?php echo e($course->duration); ?></span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <span data-course-level><?php echo e($course->level_indonesian); ?></span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <span><?php echo e(number_format($course->total_students)); ?> siswa</span>
                        </div>
                        <?php if($course->average_rating > 0): ?>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 mr-1 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                <span><?php echo e(number_format($course->average_rating, 1)); ?> (<?php echo e(number_format($course->total_reviews)); ?> ulasan)</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Course Card -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-lg p-6 sticky top-6">
                        <!-- Course Image -->
                        <div class="mb-6">
                            <?php if($course->thumbnail): ?>
                                <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>" alt="<?php echo e($course->title); ?>" class="w-full h-48 object-cover rounded-lg">
                            <?php else: ?>
                                <div class="w-full h-48 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Price -->
                        <div class="text-center mb-6">
                            <?php if($course->price == 0): ?>
                                <div class="text-3xl font-bold text-green-600" data-course-price>GRATIS</div>
                                <p class="text-gray-600 text-sm mt-1">Akses selamanya</p>
                            <?php else: ?>
                                <div class="text-3xl font-bold text-primary" data-course-price><?php echo e($course->formatted_price); ?></div>
                                <p class="text-gray-600 text-sm mt-1">Akses selamanya</p>
                            <?php endif; ?>
                        </div>

                        <!-- Action Buttons -->
                        <div class="space-y-3">
                            <?php if(auth()->guard()->check()): ?>
                                <?php
                                    $isEnrolled = auth()->user()->enrollments()
                                        ->where('course_id', $course->id)
                                        ->where('status', 'active')
                                        ->exists();
                                    $isTutor = $course->tutor_id === auth()->id();
                                ?>

                                <?php if($isTutor): ?>
                                    <a href="<?php echo e(route('tutor.curriculum.index', $course)); ?>" class="w-full btn btn-primary">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        Kelola Kurikulum
                                    </a>
                                <?php elseif($isEnrolled): ?>
                                    <a href="<?php echo e(route('course.learn', $course)); ?>" class="w-full btn btn-primary">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-6V7a2 2 0 00-2-2H5a2 2 0 00-2 2v3m2 4h10a2 2 0 002-2v-3a2 2 0 00-2-2H5a2 2 0 00-2 2v3z"></path>
                                        </svg>
                                        Lanjutkan Belajar
                                    </a>
                                <?php else: ?>
                                    <form action="<?php echo e(route('course.enroll', $course)); ?>" method="POST" class="w-full">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="w-full btn btn-primary">
                                            <?php if($course->price == 0): ?>
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                </svg>
                                                Mulai Belajar Gratis
                                            <?php else: ?>
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                                </svg>
                                                Daftar Sekarang - <?php echo e($course->formatted_price); ?>

                                            <?php endif; ?>
                                        </button>
                                    </form>
                                <?php endif; ?>
                            <?php else: ?>
                                <a href="<?php echo e(route('login')); ?>" class="w-full btn btn-primary">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                    </svg>
                                    Masuk untuk Mendaftar
                                </a>
                            <?php endif; ?>
                            <button class="w-full btn btn-outline">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                Simpan ke Wishlist
                            </button>
                        </div>

                        <!-- Course Includes -->
                        <div class="mt-6 pt-6 border-t border-gray-200">
                            <h4 class="font-semibold text-gray-900 mb-3">Kursus ini termasuk:</h4>
                            <ul class="space-y-2 text-sm text-gray-600">
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <?php echo e($totalLessons); ?> materi pembelajaran
                                </li>
                                <?php if(isset($lessonTypes['video']) && $lessonTypes['video'] > 0): ?>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <?php echo e($lessonTypes['video']); ?> video pembelajaran
                                </li>
                                <?php endif; ?>
                                <?php if(isset($lessonTypes['quiz']) && $lessonTypes['quiz'] > 0): ?>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <?php echo e($lessonTypes['quiz']); ?> kuis interaktif
                                </li>
                                <?php endif; ?>
                                <?php if(isset($lessonTypes['assignment']) && $lessonTypes['assignment'] > 0): ?>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <?php echo e($lessonTypes['assignment']); ?> tugas praktik
                                </li>
                                <?php endif; ?>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <?php echo e(floor($totalDuration / 60)); ?> jam konten
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Akses selamanya
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Sertifikat penyelesaian
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Akses di mobile dan desktop
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Description -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Tentang Kursus Ini</h2>
                    <div class="prose max-w-none text-gray-600">
                        <p><?php echo e($course->long_description ?: $course->description); ?></p>
                    </div>
                </div>

                <!-- Learning Outcomes -->
                <?php if($course->learning_outcomes): ?>
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Yang Akan Anda Pelajari</h2>
                        <ul class="grid md:grid-cols-2 gap-3">
                            <?php $__currentLoopData = $course->learning_outcomes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $outcome): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 mr-3 text-green-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700"><?php echo e($outcome); ?></span>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <!-- Requirements -->
                <?php if($course->requirements): ?>
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Persyaratan</h2>
                        <ul class="space-y-2">
                            <?php $__currentLoopData = $course->requirements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 mr-3 text-gray-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                    <span class="text-gray-700"><?php echo e($requirement); ?></span>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <!-- Target Audience -->
                <?php if($course->target_audience): ?>
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Untuk Siapa Kursus Ini</h2>
                        <ul class="space-y-2">
                            <?php $__currentLoopData = $course->target_audience; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $audience): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 mr-3 text-primary mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span class="text-gray-700"><?php echo e($audience); ?></span>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <!-- Course Curriculum -->
                <?php if($course->chapters->count() > 0): ?>
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Kurikulum Kursus</h2>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $course->chapters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chapter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border border-gray-200 rounded-lg">
                                    <div class="p-4 bg-gray-50 border-b border-gray-200">
                                        <div class="flex items-center justify-between">
                                            <h3 class="font-semibold text-gray-900"><?php echo e($chapter->title); ?></h3>
                                            <span class="text-sm text-gray-600"><?php echo e($chapter->lessons->count()); ?> materi</span>
                                        </div>
                                        <?php if($chapter->description): ?>
                                            <p class="text-sm text-gray-600 mt-1"><?php echo e($chapter->description); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="p-4">
                                        <div class="space-y-3">
                                            <?php $__currentLoopData = $chapter->lessons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lesson): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="flex items-center justify-between py-2">
                                                    <div class="flex items-center space-x-3">
                                                        <?php if($lesson->type === 'video'): ?>
                                                            <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                            </svg>
                                                        <?php elseif($lesson->type === 'text'): ?>
                                                            <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                            </svg>
                                                        <?php elseif($lesson->type === 'quiz'): ?>
                                                            <svg class="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                        <?php elseif($lesson->type === 'assignment'): ?>
                                                            <svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                                            </svg>
                                                        <?php endif; ?>
                                                        <div>
                                                            <h4 class="text-sm font-medium text-gray-900"><?php echo e($lesson->title); ?></h4>
                                                            <?php if($lesson->description): ?>
                                                                <p class="text-xs text-gray-600"><?php echo e(Str::limit($lesson->description, 60)); ?></p>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                                                        <?php if($lesson->type === 'quiz' && $lesson->quiz): ?>
                                                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">
                                                                <?php echo e($lesson->quiz->total_questions); ?> soal
                                                            </span>
                                                        <?php elseif($lesson->type === 'assignment' && $lesson->assignment): ?>
                                                            <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs">
                                                                <?php echo e($lesson->assignment->max_points); ?> poin
                                                            </span>
                                                        <?php endif; ?>
                                                        <?php if($lesson->duration_minutes): ?>
                                                            <span><?php echo e($lesson->duration_minutes); ?> menit</span>
                                                        <?php endif; ?>
                                                        <?php if($lesson->is_preview): ?>
                                                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Preview</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Instructor -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Instruktur</h3>
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                            <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900"><?php echo e($course->tutor->name); ?></h4>
                            <?php if($course->tutor->job_title): ?>
                                <p class="text-sm text-gray-600"><?php echo e($course->tutor->job_title); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php if($course->tutor->bio): ?>
                        <p class="text-sm text-gray-600 mt-4"><?php echo e(Str::limit($course->tutor->bio, 150)); ?></p>
                    <?php endif; ?>
                </div>

                <!-- Related Courses -->
                <?php if($relatedCourses->count() > 0): ?>
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4">Kursus Terkait</h3>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $relatedCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedCourse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex space-x-3">
                                    <div class="w-16 h-12 bg-gradient-to-br from-primary to-secondary rounded flex-shrink-0"></div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm font-medium text-gray-900 line-clamp-2">
                                            <a href="<?php echo e(route('course.show', $relatedCourse)); ?>" class="hover:text-primary">
                                                <?php echo e($relatedCourse->title); ?>

                                            </a>
                                        </h4>
                                        <p class="text-xs text-gray-600 mt-1">
                                            <?php if($relatedCourse->price == 0): ?>
                                                Gratis
                                            <?php else: ?>
                                                <?php echo e($relatedCourse->formatted_price); ?>

                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/course-detail.blade.php ENDPATH**/ ?>