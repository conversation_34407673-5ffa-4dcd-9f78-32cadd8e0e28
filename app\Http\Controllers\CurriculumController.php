<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Category;
use Illuminate\Http\Request;

class CurriculumController extends Controller
{
    /**
     * Display the courses page with all available courses.
     */
    public function index(Request $request)
    {
        // Get all active categories for filter
        $categories = Category::active()->orderBy('sort_order')->get();

        // Start building the query with optimized eager loading
        $query = Course::with(['tutor:id,name', 'category:id,name,slug'])
            ->published();

        // Apply sorting
        $sort = $request->get('sort', 'newest');
        switch ($sort) {
            case 'popular':
                $query->orderBy('total_students', 'desc');
                break;
            case 'rating':
                $query->orderBy('average_rating', 'desc');
                break;
            case 'price_low':
                $query->orderBy('price', 'asc');
                break;
            case 'newest':
            default:
                $query->orderBy('is_featured', 'desc')
                      ->orderBy('created_at', 'desc');
                break;
        }

        // Apply filters
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        if ($request->filled('level')) {
            $query->where('level', $request->level);
        }

        if ($request->filled('price_type')) {
            if ($request->price_type === 'free') {
                $query->where('price', 0);
            } elseif ($request->price_type === 'paid') {
                $query->where('price', '>', 0);
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereJsonContains('tags', $search);
            });
        }

        // Get courses with pagination (10 items per page as per user preference)
        $courses = $query->paginate(10)->withQueryString();

        // Get course statistics with caching for better performance
        $stats = [
            'total_courses' => Course::published()->count(),
            'free_courses' => Course::published()->where('price', 0)->count(),
            'paid_courses' => Course::published()->where('price', '>', 0)->count(),
            'total_students' => Course::published()->sum('total_students'),
        ];

        // SEO data
        $seoData = [
            'title' => 'Kursus Teknologi Terbaik - Ngambiskuy',
            'description' => 'Temukan kursus teknologi terbaik untuk mengembangkan skill programming, data science, AI, dan web development. Belajar dari instruktur berpengalaman dengan kurikulum terupdate.',
            'keywords' => 'kursus programming, belajar coding, kursus teknologi, web development, data science, artificial intelligence, online course Indonesia',
            'canonical' => request()->url(),
        ];

        return view('courses', compact('courses', 'categories', 'stats', 'seoData'));
    }

    /**
     * Show a specific course detail.
     */
    public function show(Course $course)
    {
        // Make sure the course is published
        if ($course->status !== 'published') {
            abort(404);
        }

        // Load relationships including chapters and lessons with their quiz/assignment data
        $course->load([
            'tutor',
            'category',
            'chapters' => function ($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lessons' => function ($query) {
                $query->where('is_published', true)->orderBy('sort_order');
            },
            'chapters.lessons.quiz',
            'chapters.lessons.assignment'
        ]);

        // Calculate course statistics
        $totalLessons = $course->chapters->sum(function ($chapter) {
            return $chapter->lessons->count();
        });

        $lessonTypes = $course->chapters->flatMap(function ($chapter) {
            return $chapter->lessons->pluck('type');
        })->countBy();

        $totalDuration = $course->chapters->sum(function ($chapter) {
            return $chapter->lessons->sum('duration_minutes');
        });

        // Get related courses (same category, different course)
        $relatedCourses = Course::with(['tutor', 'category'])
            ->published()
            ->where('category_id', $course->category_id)
            ->where('id', '!=', $course->id)
            ->limit(3)
            ->get();

        return view('course-detail', compact('course', 'relatedCourses', 'totalLessons', 'lessonTypes', 'totalDuration'));
    }
}
