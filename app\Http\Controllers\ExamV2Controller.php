<?php

namespace App\Http\Controllers;

use App\Models\Exam;
use App\Models\ExamAttempt;
use App\Models\ExamEnrollment;
use App\Models\ExamAnswer;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ExamV2Controller extends Controller
{
    /**
     * Display a listing of published exams.
     */
    public function index(Request $request)
    {
        $query = Exam::with(['category', 'tutor'])
            ->where('is_published', true)
            ->withCount(['questions', 'enrollments']);

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Difficulty filter
        if ($request->filled('difficulty')) {
            $query->where('difficulty_level', $request->difficulty);
        }

        // Price filter
        if ($request->filled('price_filter')) {
            if ($request->price_filter === 'free') {
                $query->where('price', 0);
            } elseif ($request->price_filter === 'paid') {
                $query->where('price', '>', 0);
            }
        }

        $exams = $query->orderBy('created_at', 'desc')->paginate(12);
        $categories = Category::orderBy('name')->get();

        // Stats for hero section
        $stats = [
            'total_exams' => Exam::where('is_published', true)->count(),
            'total_participants' => ExamEnrollment::count(),
            'certificates_issued' => ExamAttempt::where('is_passed', true)->count(),
            'success_rate' => 85, // You can calculate this based on actual data
        ];

        return view('exams-v2.index', compact('exams', 'categories', 'stats'));
    }

    /**
     * Display the specified exam.
     */
    public function show(Exam $exam)
    {
        // Only show published exams to public
        if (!$exam->is_published) {
            abort(404, 'Ujian tidak ditemukan.');
        }

        $exam->load(['tutor', 'category', 'questions']);

        $stats = [
            'total_questions' => $exam->questions->count(),
            'total_points' => $exam->questions->sum('points'),
            'total_enrollments' => $exam->enrollments->count(),
            'average_score' => $exam->attempts->avg('score_percentage') ?? 0,
        ];

        // Initialize user-specific data
        $userEnrollment = null;
        $userAttempts = collect();
        $canTakeExam = false;
        $examStatus = 'not_enrolled'; // Default status
        $statusMessage = '';
        $hasActiveAttempt = false;

        if (Auth::check()) {
            $user = Auth::user();

            // Get user enrollment
            $userEnrollment = $exam->enrollments()
                ->where('user_id', $user->id)
                ->first();

            if ($userEnrollment) {
                // Get user attempts
                $userAttempts = $exam->attempts()
                    ->where('user_id', $user->id)
                    ->orderBy('created_at', 'desc')
                    ->get();

                // Check for active attempt
                $hasActiveAttempt = $exam->attempts()
                    ->where('user_id', $user->id)
                    ->where('status', 'in_progress')
                    ->exists();

                // Determine exam status and whether user can take exam
                if ($userEnrollment->payment_status !== 'paid') {
                    $examStatus = 'payment_pending';
                    $statusMessage = 'Pembayaran belum selesai. Silakan selesaikan pembayaran untuk mengakses ujian.';
                    $canTakeExam = false;
                } elseif ($userAttempts->count() >= $exam->max_attempts) {
                    $examStatus = 'max_attempts_reached';
                    $statusMessage = "Anda telah mencapai batas maksimal {$exam->max_attempts} kali percobaan untuk ujian ini.";
                    $canTakeExam = false;
                } elseif ($hasActiveAttempt) {
                    $examStatus = 'has_active_attempt';
                    $statusMessage = 'Anda memiliki ujian yang sedang berlangsung. Lanjutkan ujian atau tunggu hingga waktu habis.';
                    $canTakeExam = true;
                } else {
                    $examStatus = 'can_take';
                    $statusMessage = 'Anda dapat mengikuti ujian ini.';
                    $canTakeExam = true;
                }
            } else {
                $examStatus = 'not_enrolled';
                $statusMessage = 'Anda belum mendaftar untuk ujian ini.';
                $canTakeExam = false;
            }
        } else {
            $examStatus = 'not_logged_in';
            $statusMessage = 'Silakan login untuk mengikuti ujian ini.';
            $canTakeExam = false;
        }

        return view('exams-v2.show', compact(
            'exam',
            'stats',
            'userEnrollment',
            'userAttempts',
            'canTakeExam',
            'examStatus',
            'statusMessage',
            'hasActiveAttempt'
        ));
    }

    /**
     * Enroll user in the exam.
     */
    public function enroll(Request $request, Exam $exam)
    {
        if (!$exam->is_published) {
            return back()->with('error', 'Ujian tidak tersedia.');
        }

        $user = Auth::user();

        // Check if already enrolled
        $existingEnrollment = $exam->enrollments()->where('user_id', $user->id)->first();
        if ($existingEnrollment) {
            return back()->with('info', 'Anda sudah terdaftar dalam ujian ini.');
        }

        try {
            DB::beginTransaction();

            // Create enrollment
            $enrollment = ExamEnrollment::create([
                'exam_id' => $exam->id,
                'user_id' => $user->id,
                'amount_paid' => $exam->price,
                'payment_status' => $exam->price == 0 ? 'paid' : 'pending',
                'enrolled_at' => now(),
                'is_active' => true,
            ]);

            // If free exam, mark as paid immediately
            if ($exam->price == 0) {
                $enrollment->update(['payment_status' => 'paid']);
            }

            DB::commit();

            if ($exam->price == 0) {
                return redirect()->route('exams.take', $exam)
                    ->with('success', 'Berhasil mendaftar ujian gratis! Anda dapat langsung mengerjakan ujian.');
            } else {
                // Redirect to payment page (to be implemented)
                return back()->with('success', 'Berhasil mendaftar ujian. Silakan lakukan pembayaran untuk mengakses ujian.');
            }

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Terjadi kesalahan saat mendaftar ujian. Silakan coba lagi.');
        }
    }

    /**
     * Show the professional exam taking interface.
     */
    public function take(Request $request, Exam $exam)
    {
        if (!$exam->is_published) {
            abort(404, 'Ujian tidak tersedia.');
        }

        $user = Auth::user();

        // Check enrollment
        $enrollment = $exam->enrollments()
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->where('payment_status', 'paid')
            ->first();

        if (!$enrollment) {
            return redirect()->route('exams.show', $exam)
                ->with('error', 'Anda belum terdaftar atau belum melakukan pembayaran untuk ujian ini.');
        }

        // Check attempt limit
        $attemptCount = $exam->attempts()->where('user_id', $user->id)->count();
        if ($attemptCount >= $exam->max_attempts) {
            return redirect()->route('exams.show', $exam)
                ->with('error', 'Anda telah mencapai batas maksimal percobaan untuk ujian ini.');
        }

        // Check if there's an ongoing attempt
        $ongoingAttempt = $exam->attempts()
            ->where('user_id', $user->id)
            ->where('status', 'in_progress')
            ->first();

        if ($ongoingAttempt) {
            // Check if attempt has expired
            $timeLimit = $exam->time_limit * 60; // Convert to seconds
            $timeElapsed = now()->diffInSeconds($ongoingAttempt->started_at);

            if ($timeElapsed >= $timeLimit) {
                // Auto-submit expired attempt
                $this->autoSubmitAttempt($ongoingAttempt);
                return redirect()->route('exams.result', [$exam, $ongoingAttempt])
                    ->with('warning', 'Waktu ujian telah habis. Ujian telah otomatis diselesaikan.');
            }

            $attempt = $ongoingAttempt;
        } else {
            // Ensure no incomplete attempts exist before creating new one
            $incompleteAttempts = $exam->attempts()
                ->where('user_id', $user->id)
                ->whereIn('status', ['in_progress', 'started'])
                ->get();

            foreach ($incompleteAttempts as $incompleteAttempt) {
                $this->autoSubmitAttempt($incompleteAttempt);
            }

            // Create new attempt
            $attempt = ExamAttempt::create([
                'exam_id' => $exam->id,
                'user_id' => $user->id,
                'attempt_number' => $attemptCount + 1,
                'started_at' => now(),
                'status' => 'in_progress',
                'total_questions' => $exam->questions->count(),
                'max_points' => $exam->questions->sum('points'),
            ]);
        }

        $exam->load(['questions.options']);

        // Shuffle questions if enabled (only for new attempts)
        $questions = $exam->shuffle_questions && !$ongoingAttempt
            ? $exam->questions->shuffle()
            : $exam->questions;

        // Get current question index from request, default to 1
        $currentQuestionIndex = max(1, min($request->get('question', 1), $questions->count()));
        $currentQuestion = $questions->skip($currentQuestionIndex - 1)->first();

        // Get existing answers for this attempt
        $existingAnswers = ExamAnswer::where('attempt_id', $attempt->id)
            ->get()
            ->keyBy('question_id')
            ->map(function ($answer) {
                // For multiple choice/true-false, return the selected option ID
                if ($answer->selected_option_id) {
                    return $answer->selected_option_id;
                }
                // For short answer, return the text
                if ($answer->answer_text) {
                    return $answer->answer_text;
                }
                // Return null if no answer
                return null;
            })
            ->filter(function ($value) {
                // Only keep non-null values
                return $value !== null;
            })
            ->toArray();

        $timeElapsed = now()->diffInSeconds($attempt->started_at);
        $timeLimit = $exam->time_limit * 60; // Convert to seconds
        $timeRemaining = max(0, $timeLimit - $timeElapsed);

        // If time has expired, auto-submit the attempt
        if ($timeRemaining <= 0) {
            $this->autoSubmitAttempt($attempt);
            return redirect()->route('exams.result', [$exam, $attempt])
                ->with('warning', 'Waktu ujian telah habis. Ujian telah otomatis diselesaikan.');
        }

        return view('exams-v2.take', compact(
            'exam',
            'attempt',
            'questions',
            'currentQuestion',
            'currentQuestionIndex',
            'existingAnswers',
            'timeRemaining'
        ));
    }

    /**
     * Auto-save answer for a specific question during exam.
     */
    public function autoSave(Request $request, Exam $exam)
    {
        $user = Auth::user();

        // Get the current attempt
        $attempt = $exam->attempts()
            ->where('user_id', $user->id)
            ->where('status', 'in_progress')
            ->first();

        if (!$attempt) {
            return response()->json(['error' => 'No active exam attempt found'], 404);
        }

        $validated = $request->validate([
            'question_id' => 'required|exists:exam_questions,id',
            'selected_option_id' => 'nullable|exists:exam_question_options,id',
            'answer_text' => 'nullable|string',
        ]);

        try {
            // Save or update the answer
            $answer = ExamAnswer::updateOrCreate(
                [
                    'attempt_id' => $attempt->id,
                    'question_id' => $validated['question_id'],
                ],
                [
                    'selected_option_id' => $validated['selected_option_id'] ?? null,
                    'answer_text' => $validated['answer_text'] ?? null,
                    'answered_at' => now(),
                ]
            );

            // Log the save for debugging
            Log::info('Answer auto-saved', [
                'attempt_id' => $attempt->id,
                'question_id' => $validated['question_id'],
                'selected_option_id' => $validated['selected_option_id'] ?? null,
                'answer_text' => $validated['answer_text'] ?? null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Answer saved successfully',
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            Log::error('Auto-save failed', [
                'error' => $e->getMessage(),
                'attempt_id' => $attempt->id,
                'question_id' => $validated['question_id'],
            ]);

            return response()->json([
                'error' => 'Failed to save answer',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Submit the exam attempt.
     */
    public function submit(Request $request, Exam $exam)
    {
        $user = Auth::user();

        // Get the ongoing attempt
        $attempt = $exam->attempts()
            ->where('user_id', $user->id)
            ->where('status', 'in_progress')
            ->first();

        if (!$attempt) {
            return redirect()->route('exams.show', $exam)
                ->with('error', 'Tidak ada ujian yang sedang berlangsung.');
        }

        try {
            DB::beginTransaction();

            // Get all answers for this attempt
            $answers = ExamAnswer::where('attempt_id', $attempt->id)->get();
            $correctAnswers = 0;
            $totalPoints = 0;

            // Calculate score based on existing answers
            foreach ($answers as $answer) {
                $question = $exam->questions()->find($answer->question_id);
                if (!$question) continue;

                $isCorrect = false;
                $pointsEarned = 0;

                if ($question->type === 'multiple_choice' || $question->type === 'true_false') {
                    if ($answer->selected_option_id) {
                        $selectedOption = $question->options()->find($answer->selected_option_id);
                        if ($selectedOption && $selectedOption->is_correct) {
                            $isCorrect = true;
                            $pointsEarned = $question->points;
                            $correctAnswers++;
                        }
                    }
                } elseif ($question->type === 'short_answer') {
                    // Short answer questions need manual grading
                    $isCorrect = null;
                    $pointsEarned = 0;
                }

                // Update answer with calculated results
                $answer->update([
                    'is_correct' => $isCorrect,
                    'points_earned' => $pointsEarned,
                ]);

                $totalPoints += $pointsEarned;
            }

            // Calculate score
            $scorePercentage = $attempt->max_points > 0
                ? ($totalPoints / $attempt->max_points) * 100
                : 0;

            $isPassed = $scorePercentage >= $exam->passing_score;

            // Update attempt
            $attempt->update([
                'completed_at' => now(),
                'submitted_at' => now(),
                'time_taken' => now()->diffInSeconds($attempt->started_at),
                'answered_questions' => $answers->count(),
                'correct_answers' => $correctAnswers,
                'score_percentage' => $scorePercentage,
                'total_points' => $totalPoints,
                'is_passed' => $isPassed,
                'status' => 'completed',
            ]);

            // Update enrollment stats
            $enrollment = $exam->enrollments()->where('user_id', $user->id)->first();
            if ($enrollment) {
                $enrollment->update([
                    'attempts_used' => $enrollment->attempts_used + 1,
                    'best_score' => max($enrollment->best_score ?? 0, $scorePercentage),
                    'has_passed' => $enrollment->has_passed || $isPassed,
                    'last_attempt_at' => now(),
                ]);

                if ($isPassed && !$enrollment->passed_at) {
                    $enrollment->update(['passed_at' => now()]);
                }
            }

            DB::commit();

            return redirect()->route('exams.result', [$exam, $attempt])
                ->with('success', 'Ujian berhasil diselesaikan!');

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Exam submission failed', [
                'error' => $e->getMessage(),
                'attempt_id' => $attempt->id,
                'exam_id' => $exam->id,
                'user_id' => $user->id,
            ]);
            return back()->with('error', 'Terjadi kesalahan saat menyimpan jawaban. Silakan coba lagi.');
        }
    }

    /**
     * Show exam result.
     */
    public function result(Exam $exam, ExamAttempt $attempt)
    {
        $user = Auth::user();

        // Ensure the attempt belongs to the authenticated user
        if ($attempt->user_id !== $user->id || $attempt->exam_id !== $exam->id) {
            abort(403, 'Akses tidak diizinkan.');
        }

        $attempt->load(['answers.question', 'answers.selectedOption']);

        $enrollment = $exam->enrollments()->where('user_id', $user->id)->first();

        return view('exams-v2.result', compact('exam', 'attempt', 'enrollment'));
    }

    /**
     * Auto-submit an expired attempt.
     */
    private function autoSubmitAttempt(ExamAttempt $attempt)
    {
        try {
            DB::beginTransaction();

            // Get all answers for this attempt and calculate scores
            $answers = ExamAnswer::where('attempt_id', $attempt->id)->get();
            $correctAnswers = 0;
            $totalPoints = 0;

            // Calculate score based on existing answers (same logic as submit method)
            foreach ($answers as $answer) {
                $question = $attempt->exam->questions()->find($answer->question_id);
                if (!$question) continue;

                $isCorrect = false;
                $pointsEarned = 0;

                if ($question->type === 'multiple_choice' || $question->type === 'true_false') {
                    if ($answer->selected_option_id) {
                        $selectedOption = $question->options()->find($answer->selected_option_id);
                        if ($selectedOption && $selectedOption->is_correct) {
                            $isCorrect = true;
                            $pointsEarned = $question->points;
                            $correctAnswers++;
                        }
                    }
                } elseif ($question->type === 'short_answer') {
                    // Short answer questions need manual grading
                    $isCorrect = null;
                    $pointsEarned = 0;
                }

                // Update answer with calculated results
                $answer->update([
                    'is_correct' => $isCorrect,
                    'points_earned' => $pointsEarned,
                ]);

                $totalPoints += $pointsEarned;
            }

            $scorePercentage = $attempt->max_points > 0
                ? ($totalPoints / $attempt->max_points) * 100
                : 0;

            $isPassed = $scorePercentage >= $attempt->exam->passing_score;

            $attempt->update([
                'completed_at' => now(),
                'submitted_at' => now(),
                'time_taken' => $attempt->exam->time_limit * 60,
                'answered_questions' => $answers->count(),
                'correct_answers' => $correctAnswers,
                'score_percentage' => $scorePercentage,
                'total_points' => $totalPoints,
                'is_passed' => $isPassed,
                'status' => 'expired',
            ]);

            // Update enrollment stats
            $enrollment = $attempt->exam->enrollments()->where('user_id', $attempt->user_id)->first();
            if ($enrollment) {
                $enrollment->update([
                    'attempts_used' => $enrollment->attempts_used + 1,
                    'best_score' => max($enrollment->best_score ?? 0, $scorePercentage),
                    'has_passed' => $enrollment->has_passed || ($scorePercentage >= $attempt->exam->passing_score),
                    'last_attempt_at' => now(),
                ]);

                if ($scorePercentage >= $attempt->exam->passing_score && !$enrollment->passed_at) {
                    $enrollment->update(['passed_at' => now()]);
                }
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Auto-submit failed', [
                'error' => $e->getMessage(),
                'attempt_id' => $attempt->id,
            ]);
        }
    }
}
