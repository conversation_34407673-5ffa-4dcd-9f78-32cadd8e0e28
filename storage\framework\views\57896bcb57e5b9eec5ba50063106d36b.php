<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', 'Ujian - Ngambiskuy'); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Additional Styles -->
    <style>
        /* Custom styles for exam interface */
        .exam-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .question-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .question-card:hover {
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .option-card {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .option-card:hover::before {
            left: 100%;
        }

        .option-card:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .option-card.selected {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
        }

        .option-card.selected .option-letter {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            transform: scale(1.1);
        }
        
        .timer-warning {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }
        
        /* Question navigation styles */
        .question-nav-btn {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .question-nav-btn.answered {
            background-color: #10b981;
            color: white;
            border: 2px solid #10b981;
        }
        
        .question-nav-btn.current {
            background-color: #3b82f6;
            color: white;
            border: 2px solid #3b82f6;
        }
        
        .question-nav-btn.unanswered {
            background-color: #f3f4f6;
            color: #6b7280;
            border: 2px solid #d1d5db;
        }
        
        .question-nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        /* Auto-save indicator */
        .auto-save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 10px 18px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .auto-save-indicator.saving {
            background: linear-gradient(135deg, rgba(251, 191, 36, 0.9), rgba(245, 158, 11, 0.9));
            color: #92400e;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }

        .auto-save-indicator.saved {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 0.9));
            color: white;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .auto-save-indicator.error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 38, 0.9));
            color: white;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        /* Loading spinner */
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Smooth page transitions */
        .page-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .page-transition.loaded {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .question-nav-btn {
                width: 35px;
                height: 35px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body class="font-sans antialiased">
    <div class="exam-container">
        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!-- Auto-save indicator -->
    <div id="autoSaveIndicator" class="auto-save-indicator" style="display: none;">
        <span id="autoSaveText">Menyimpan...</span>
    </div>

    <!-- Scripts -->
    <script>
        // Global exam utilities
        window.ExamUtils = {
            // Auto-save functionality
            autoSave: function(examId, questionId, data) {
                return new Promise((resolve, reject) => {
                    const indicator = document.getElementById('autoSaveIndicator');
                    const text = document.getElementById('autoSaveText');
                    
                    // Show saving indicator
                    indicator.className = 'auto-save-indicator saving';
                    indicator.style.display = 'block';
                    text.innerHTML = '<span class="loading-spinner mr-2"></span>Menyimpan...';
                    
                    fetch(`/exams/${examId}/auto-save`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            question_id: questionId,
                            ...data
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Show success indicator
                            indicator.className = 'auto-save-indicator saved';
                            text.textContent = 'Tersimpan';
                            
                            // Hide after 2 seconds
                            setTimeout(() => {
                                indicator.style.display = 'none';
                            }, 2000);
                            
                            resolve(data);
                        } else {
                            throw new Error(data.message || 'Failed to save');
                        }
                    })
                    .catch(error => {
                        // Show error indicator
                        indicator.className = 'auto-save-indicator error';
                        text.textContent = 'Gagal menyimpan';
                        
                        // Hide after 3 seconds
                        setTimeout(() => {
                            indicator.style.display = 'none';
                        }, 3000);
                        
                        console.error('Auto-save error:', error);
                        reject(error);
                    });
                });
            },
            
            // Timer utilities
            formatTime: function(seconds) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = seconds % 60;
                
                return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            },
            
            // Progress calculation
            calculateProgress: function(answered, total) {
                return total > 0 ? Math.round((answered / total) * 100) : 0;
            },
            
            // Question navigation
            updateQuestionStatus: function(questionIndex, isAnswered) {
                const btn = document.querySelector(`[data-question="${questionIndex}"]`);
                if (btn) {
                    btn.classList.remove('answered', 'unanswered');
                    btn.classList.add(isAnswered ? 'answered' : 'unanswered');
                }
            },
            
            // Confirmation dialogs
            confirmSubmit: function(unansweredCount) {
                if (unansweredCount > 0) {
                    return confirm(`Anda masih memiliki ${unansweredCount} soal yang belum dijawab. Apakah Anda yakin ingin menyelesaikan ujian?`);
                } else {
                    return confirm('Apakah Anda yakin ingin menyelesaikan ujian? Anda tidak dapat mengubah jawaban setelah ini.');
                }
            }
        };
        
        // Prevent accidental page refresh (only for external navigation)
        let isInternalNavigation = false;

        window.addEventListener('beforeunload', function(e) {
            // Only show warning for external navigation (refresh, close tab, etc.)
            if (!isInternalNavigation) {
                e.preventDefault();
                e.returnValue = 'Anda yakin ingin meninggalkan halaman? Progress ujian mungkin akan hilang.';
            }
        });

        // Mark internal navigation
        window.markInternalNavigation = function() {
            isInternalNavigation = true;
            setTimeout(() => {
                isInternalNavigation = false;
            }, 1000);
        };
        
        // Disable right-click context menu during exam
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // Disable certain keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Disable F12, Ctrl+Shift+I, Ctrl+U, etc.
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
            }
        });
    </script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/layouts/exam-v2.blade.php ENDPATH**/ ?>