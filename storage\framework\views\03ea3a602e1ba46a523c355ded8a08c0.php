<?php $__env->startSection('title', $exam->title . ' - <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Breadcrumb -->
    <div class="bg-white border-b">
        <div class="container mx-auto px-4 py-4">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="<?php echo e(route('home')); ?>" class="text-gray-700 hover:text-blue-600">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                            </svg>
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <a href="<?php echo e(route('exams.index')); ?>" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">Ujian</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="ml-1 text-gray-500 md:ml-2"><?php echo e($exam->title); ?></span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Exam Header -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex flex-wrap gap-2 mb-4">
                        <?php if($exam->category): ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <?php echo e($exam->category->name); ?>

                            </span>
                        <?php endif; ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <?php echo e($exam->difficulty_label); ?>

                        </span>
                        <?php if($exam->certificate_enabled): ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                </svg>
                                Sertifikat
                            </span>
                        <?php endif; ?>
                    </div>

                    <h1 class="text-3xl font-bold text-gray-900 mb-4"><?php echo e($exam->title); ?></h1>
                    <p class="text-gray-600 text-lg mb-6"><?php echo e($exam->description); ?></p>

                    <!-- Exam Stats -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600"><?php echo e($stats['total_questions']); ?></div>
                            <div class="text-sm text-gray-600">Soal</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600"><?php echo e($exam->time_limit); ?></div>
                            <div class="text-sm text-gray-600">Menit</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-yellow-600"><?php echo e($exam->passing_score); ?>%</div>
                            <div class="text-sm text-gray-600">Nilai Lulus</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600"><?php echo e($exam->max_attempts); ?></div>
                            <div class="text-sm text-gray-600">Percobaan</div>
                        </div>
                    </div>

                    <!-- Tutor Info -->
                    <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                        <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mr-4">
                            <span class="text-lg font-medium text-gray-600">
                                <?php echo e(substr($exam->tutor->name, 0, 1)); ?>

                            </span>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900"><?php echo e($exam->tutor->name); ?></h3>
                            <p class="text-sm text-gray-600">Tutor</p>
                        </div>
                    </div>
                </div>

                <!-- Exam Instructions -->
                <?php if($exam->instructions): ?>
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Instruksi Ujian</h2>
                        <div class="prose prose-gray max-w-none">
                            <p class="text-gray-700"><?php echo e($exam->instructions); ?></p>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Exam Features -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Fitur Ujian</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-700"><?php echo e($stats['total_questions']); ?> soal pilihan ganda</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-700">Batas waktu <?php echo e($exam->time_limit); ?> menit</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-gray-700">Maksimal <?php echo e($exam->max_attempts); ?> percobaan</span>
                        </div>
                        <?php if($exam->show_results_immediately): ?>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">Hasil langsung setelah ujian</span>
                            </div>
                        <?php endif; ?>
                        <?php if($exam->certificate_enabled): ?>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">Sertifikat digital</span>
                            </div>
                        <?php endif; ?>
                        <?php if($exam->shuffle_questions): ?>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">Soal diacak</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- User Attempts (if logged in and enrolled) -->
                <?php if(auth()->guard()->check()): ?>
                    <?php if($userEnrollment && $userAttempts->count() > 0): ?>
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-xl font-semibold text-gray-900 mb-4">Riwayat Percobaan Anda</h2>
                            <div class="space-y-3">
                                <?php $__currentLoopData = $userAttempts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attempt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                        <div>
                                            <div class="font-medium text-gray-900">
                                                Percobaan <?php echo e($attempt->attempt_number); ?>

                                            </div>
                                            <div class="text-sm text-gray-600">
                                                <?php echo e($attempt->created_at->format('d M Y, H:i')); ?>

                                                <?php if($attempt->status === 'completed'): ?>
                                                    • Skor: <?php echo e(number_format($attempt->score_percentage, 1)); ?>%
                                                    <?php if($attempt->is_passed): ?>
                                                        <span class="text-green-600 font-medium">• Lulus</span>
                                                    <?php else: ?>
                                                        <span class="text-red-600 font-medium">• Tidak Lulus</span>
                                                    <?php endif; ?>
                                                <?php elseif($attempt->status === 'in_progress'): ?>
                                                    <span class="text-blue-600 font-medium">• Sedang Berlangsung</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php if($attempt->status === 'completed'): ?>
                                            <a href="<?php echo e(route('exams.result', [$exam, $attempt])); ?>" class="btn btn-sm btn-outline">
                                                Lihat Hasil
                                            </a>
                                        <?php elseif($attempt->status === 'in_progress'): ?>
                                            <a href="<?php echo e(route('exams.take', $exam)); ?>" class="btn btn-sm bg-blue-600 hover:bg-blue-700 text-white">
                                                Lanjutkan
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Enrollment Card -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6 sticky top-6">
                    <div class="text-center mb-6">
                        <div class="text-3xl font-bold text-gray-900 mb-2"><?php echo e($exam->formatted_price); ?></div>
                        <?php if($exam->price > 0): ?>
                            <p class="text-sm text-gray-600">Sekali bayar, akses selamanya</p>
                        <?php endif; ?>
                    </div>

                    <?php if(auth()->guard()->check()): ?>
                        <?php if($userEnrollment): ?>
                            <?php if($userEnrollment->payment_status === 'paid'): ?>
                                <?php if($canTakeExam): ?>
                                    <a href="<?php echo e(route('exams.take', $exam)); ?>" class="w-full btn bg-green-600 hover:bg-green-700 text-white mb-4">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M7 16a3 3 0 006 0v-2"></path>
                                        </svg>
                                        Mulai Ujian
                                    </a>
                                <?php else: ?>
                                    <div class="w-full btn bg-gray-400 text-white mb-4 cursor-not-allowed">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                        </svg>
                                        Batas Percobaan Tercapai
                                    </div>
                                <?php endif; ?>

                                <div class="text-center text-sm text-gray-600 mb-4">
                                    Percobaan: <?php echo e($userAttempts->count()); ?>/<?php echo e($exam->max_attempts); ?>

                                </div>

                                <?php if($userEnrollment->has_passed): ?>
                                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                            <div>
                                                <div class="font-medium text-green-800">Selamat!</div>
                                                <div class="text-sm text-green-700">Anda telah lulus ujian ini</div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php else: ?>
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                    <div class="text-center">
                                        <div class="font-medium text-yellow-800 mb-2">Menunggu Pembayaran</div>
                                        <div class="text-sm text-yellow-700">Silakan lakukan pembayaran untuk mengakses ujian</div>
                                    </div>
                                </div>
                                <button class="w-full btn bg-blue-600 hover:bg-blue-700 text-white mb-4">
                                    Bayar Sekarang
                                </button>
                            <?php endif; ?>
                        <?php else: ?>
                            <form action="<?php echo e(route('exams.enroll', $exam)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="w-full btn bg-blue-600 hover:bg-blue-700 text-white mb-4">
                                    <?php if($exam->price == 0): ?>
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Daftar Gratis
                                    <?php else: ?>
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 3H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                                        </svg>
                                        Daftar Ujian
                                    <?php endif; ?>
                                </button>
                            </form>
                        <?php endif; ?>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="w-full btn bg-blue-600 hover:bg-blue-700 text-white mb-4">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                            Login untuk Mengikuti Ujian
                        </a>
                    <?php endif; ?>

                    <!-- Exam Info -->
                    <div class="space-y-3 text-sm">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Total Soal</span>
                            <span class="font-medium"><?php echo e($stats['total_questions']); ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Durasi</span>
                            <span class="font-medium"><?php echo e($exam->time_limit); ?> menit</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Nilai Lulus</span>
                            <span class="font-medium"><?php echo e($exam->passing_score); ?>%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Percobaan</span>
                            <span class="font-medium"><?php echo e($exam->max_attempts); ?>x</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Peserta</span>
                            <span class="font-medium"><?php echo e($stats['total_enrollments']); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Share Card -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="font-semibold text-gray-900 mb-4">Bagikan Ujian</h3>
                    <div class="flex space-x-3">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(request()->fullUrl())); ?>"
                           target="_blank"
                           class="flex-1 btn btn-outline text-blue-600 border-blue-600 hover:bg-blue-50">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                            Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode(request()->fullUrl())); ?>&text=<?php echo e(urlencode($exam->title)); ?>"
                           target="_blank"
                           class="flex-1 btn btn-outline text-blue-400 border-blue-400 hover:bg-blue-50">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                            Twitter
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/exams/show.blade.php ENDPATH**/ ?>