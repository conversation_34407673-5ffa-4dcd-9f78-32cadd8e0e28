<?php $__env->startSection('title', 'NALA Membership & Pricing - Ngambiskuy'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-16">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                NALA Membership Plans
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Pilih paket membership NALA yang sesuai dengan kebutuhan belajar Anda.
                Dapatkan akses ke AI-powered learning experience terbaik di Indonesia.
            </p>
        </div>

        <!-- NALA Membership Plans -->
        <div class="mb-20" id="nala-membership">
            <h2 class="text-3xl font-bold text-center text-gray-900 mb-12">NALA Membership Plans</h2>
            <p class="text-center text-gray-600 mb-12 max-w-3xl mx-auto">
                Pilih paket yang sesuai dengan kebutuhan belajar Anda. Lihat perbandingan lengkap fitur-fitur yang tersedia untuk setiap paket membership.
            </p>



            <!-- Comprehensive Comparison Table -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <!-- Header with Plan Names and Prices -->
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-6 py-8 text-left">
                                    <div class="text-lg font-bold text-gray-900">Features</div>
                                    <div class="text-sm text-gray-600 mt-1">Compare all plans</div>
                                </th>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                    $isFreePlanForGuest = !Auth::check() && $plan->is_free;
                                ?>
                                <th class="px-6 py-8 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50 border-l-4 border-r-4 border-emerald-500' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50 border-l-4 border-r-4 border-blue-500' : ''); ?>">
                                    <?php if($isCurrentPlan): ?>
                                    <div class="inline-block bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-semibold mb-3">
                                        Current Plan
                                    </div>
                                    <?php elseif($plan->is_featured): ?>
                                    <div class="inline-block bg-emerald-500 text-white px-3 py-1 rounded-full text-xs font-semibold mb-3">
                                        Most Popular
                                    </div>
                                    <?php endif; ?>
                                    <div class="text-xl font-bold text-gray-900 mb-2"><?php echo e($plan->name); ?></div>
                                    <div class="mb-3">
                                        <?php if($plan->is_free): ?>
                                        <span class="text-3xl font-bold text-gray-900">Gratis</span>
                                        <?php else: ?>
                                        <div class="text-2xl font-bold text-gray-900">
                                            IDR <?php echo e(number_format($plan->price, 0, ',', '.')); ?>

                                        </div>
                                        <div class="text-sm text-gray-600">/bulan</div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-4"><?php echo e($plan->description); ?></div>

                                    <?php if($isCurrentPlan): ?>
                                    <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-semibold cursor-default text-sm">
                                        Current Plan
                                    </button>
                                    <?php elseif($isFreePlanForGuest || ($plan->is_free && !Auth::check())): ?>
                                    <a href="<?php echo e(route('register')); ?>"
                                       class="block w-full bg-gray-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors duration-200 text-center text-sm">
                                        Get Started
                                    </a>
                                    <?php elseif(!Auth::check()): ?>
                                    <a href="<?php echo e(route('login')); ?>"
                                       class="block w-full bg-emerald-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-200 text-center text-sm">
                                        Login to Choose
                                    </a>
                                    <?php else: ?>
                                    <a href="<?php echo e(route('payment.membership.checkout', $plan)); ?>"
                                       class="block w-full bg-emerald-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-200 text-center text-sm">
                                        <?php if($currentMembership): ?>
                                            <?php if($currentMembership->membershipPlan->sort_order > $plan->sort_order): ?>
                                                Downgrade to <?php echo e($plan->name); ?>

                                            <?php else: ?>
                                                Upgrade to <?php echo e($plan->name); ?>

                                            <?php endif; ?>
                                        <?php else: ?>
                                            Choose <?php echo e($plan->name); ?>

                                        <?php endif; ?>
                                    </a>
                                    <?php endif; ?>
                                </th>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                        </thead>

                        <!-- Feature Comparison Rows -->
                        <tbody class="divide-y divide-gray-200">
                            <!-- NALA Prompts -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        NALA AI Prompts per Day
                                    </div>
                                </td>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                ?>
                                <td class="px-6 py-4 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50' : ''); ?>">
                                    <?php if($plan->nala_prompts): ?>
                                    <div class="text-emerald-600 font-bold"><?php echo e($plan->nala_prompts); ?></div>
                                    <div class="text-xs text-gray-600">prompts/day</div>
                                    <?php elseif($plan->has_unlimited_nala): ?>
                                    <div class="text-emerald-600 font-bold">Unlimited</div>
                                    <?php else: ?>
                                    <div class="text-gray-500 font-bold">Limited</div>
                                    <div class="text-xs text-gray-600">(Throttled)</div>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>

                            <!-- Intelligent Course Engine -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Intelligent Course Engine (ICE)
                                    </div>
                                </td>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                ?>
                                <td class="px-6 py-4 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50' : ''); ?>">
                                    <?php if($plan->has_ice_full): ?>
                                    <div class="text-emerald-500 font-bold text-xl">✓</div>
                                    <div class="text-xs text-gray-600">Full Access</div>
                                    <?php else: ?>
                                    <div class="text-orange-500 font-bold text-xl">⚠</div>
                                    <div class="text-xs text-gray-600">Limited</div>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>

                            <!-- AI Teaching Assistants (Courses) -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-indigo-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        AI Teaching Assistants (Courses)
                                    </div>
                                </td>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                ?>
                                <td class="px-6 py-4 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50' : ''); ?>">
                                    <?php if($plan->has_ai_teaching_assistants_courses): ?>
                                    <div class="text-emerald-500 font-bold text-xl">✓</div>
                                    <div class="text-xs text-gray-600">Full Access</div>
                                    <?php else: ?>
                                    <div class="text-orange-500 font-bold text-xl">⚠</div>
                                    <div class="text-xs text-gray-600">Limited</div>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>

                            <!-- AI Teaching Assistants (Tryout) -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-teal-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        AI Teaching Assistants (Tryout)
                                    </div>
                                </td>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                ?>
                                <td class="px-6 py-4 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50' : ''); ?>">
                                    <?php if($plan->has_ai_teaching_assistants_tryout): ?>
                                    <div class="text-emerald-500 font-bold text-xl">✓</div>
                                    <?php else: ?>
                                    <div class="text-red-500 font-bold text-xl">✗</div>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>

                            <!-- Free Certifications -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-yellow-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        Free Course Certifications
                                    </div>
                                </td>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                ?>
                                <td class="px-6 py-4 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50' : ''); ?>">
                                    <?php if($plan->has_free_certifications): ?>
                                    <div class="text-emerald-500 font-bold text-xl">✓</div>
                                    <?php else: ?>
                                    <div class="text-red-500 font-bold text-xl">✗</div>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>

                            <!-- Blog Access -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                                        </svg>
                                        Blog Access & Creation
                                    </div>
                                </td>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                ?>
                                <td class="px-6 py-4 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50' : ''); ?>">
                                    <?php if($plan->has_blog_access): ?>
                                    <div class="text-emerald-500 font-bold text-xl">✓</div>
                                    <?php else: ?>
                                    <div class="text-red-500 font-bold text-xl">✗</div>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>

                            <!-- Tryout Exam Certificates -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        Tryout Exam Certificates
                                    </div>
                                </td>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                ?>
                                <td class="px-6 py-4 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50' : ''); ?>">
                                    <?php if($plan->slug === 'free'): ?>
                                    <div class="text-red-500 font-bold text-xl">✗</div>
                                    <?php elseif($plan->slug === 'basic'): ?>
                                    <div class="text-emerald-500 font-bold text-xl">✓</div>
                                    <div class="text-xs text-gray-600">(Standard)</div>
                                    <?php elseif($plan->slug === 'standard'): ?>
                                    <div class="text-emerald-500 font-bold text-xl">✓</div>
                                    <div class="text-xs text-gray-600">(Career Insights)</div>
                                    <?php elseif($plan->slug === 'pro'): ?>
                                    <div class="text-emerald-500 font-bold text-xl">✓</div>
                                    <div class="text-xs text-gray-600">(Premium + AI Recommendations)</div>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>

                            <!-- Career Path Predictor -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-pink-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Career Path Predictor
                                    </div>
                                </td>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                ?>
                                <td class="px-6 py-4 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50' : ''); ?>">
                                    <?php if($plan->career_path_predictor === 'basic'): ?>
                                    <div class="text-blue-500 font-bold">Basic</div>
                                    <?php elseif($plan->career_path_predictor === 'enhanced'): ?>
                                    <div class="text-emerald-500 font-bold">Enhanced</div>
                                    <?php elseif($plan->career_path_predictor === 'enhanced_with_job_board'): ?>
                                    <div class="text-purple-500 font-bold">Enhanced</div>
                                    <div class="text-xs text-gray-600">+ Job Board</div>
                                    <?php else: ?>
                                    <div class="text-red-500 font-bold text-xl">✗</div>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>

                            <!-- Priority Support -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"></path>
                                        </svg>
                                        Priority Support
                                    </div>
                                </td>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                ?>
                                <td class="px-6 py-4 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50' : ''); ?>">
                                    <?php if($plan->has_priority_support): ?>
                                    <div class="text-emerald-500 font-bold text-xl">✓</div>
                                    <?php else: ?>
                                    <div class="text-red-500 font-bold text-xl">✗</div>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                        </tbody>

                        <!-- Bottom CTA Row -->
                        <tfoot class="bg-gray-50">
                            <tr>
                                <td class="px-6 py-6 text-sm font-medium text-gray-900">
                                    <div class="text-lg font-bold">Ready to upgrade?</div>
                                    <div class="text-sm text-gray-600">Choose your plan and start learning</div>
                                </td>
                                <?php $__currentLoopData = $membershipPlans['individual'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isCurrentPlan = $currentPlanId && $currentPlanId == $plan->id;
                                    $isFreePlanForGuest = !Auth::check() && $plan->is_free;
                                ?>
                                <td class="px-6 py-6 text-center <?php echo e($plan->is_featured ? 'bg-emerald-50' : ''); ?> <?php echo e($isCurrentPlan ? 'bg-blue-50' : ''); ?>">
                                    <?php if($isCurrentPlan): ?>
                                    <button class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold cursor-default">
                                        Current Plan
                                    </button>
                                    <?php elseif($isFreePlanForGuest || ($plan->is_free && !Auth::check())): ?>
                                    <a href="<?php echo e(route('register')); ?>"
                                       class="block w-full bg-gray-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-gray-700 transition-colors duration-200 text-center">
                                        Get Started
                                    </a>
                                    <?php elseif(!Auth::check()): ?>
                                    <a href="<?php echo e(route('login')); ?>"
                                       class="block w-full bg-emerald-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-200 text-center">
                                        Login to Choose
                                    </a>
                                    <?php else: ?>
                                    <a href="<?php echo e(route('payment.membership.checkout', $plan)); ?>"
                                       class="block w-full bg-emerald-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-200 text-center">
                                        <?php if($currentMembership): ?>
                                            <?php if($currentMembership->membershipPlan->sort_order > $plan->sort_order): ?>
                                                Downgrade to <?php echo e($plan->name); ?>

                                            <?php else: ?>
                                                Upgrade to <?php echo e($plan->name); ?>

                                            <?php endif; ?>
                                        <?php else: ?>
                                            Get <?php echo e($plan->name); ?>

                                        <?php endif; ?>
                                    </a>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>

            <!-- Benefits Summary -->
            <div class="mt-12 bg-gradient-to-r from-emerald-50 to-blue-50 rounded-xl p-8">
                <h3 class="text-2xl font-bold text-center text-gray-900 mb-6">Why Upgrade to NALA Membership?</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-bold text-gray-900 mb-2">AI-Powered Learning</h4>
                        <p class="text-gray-600 text-sm">Get personalized learning experience with NALA AI assistant and intelligent course recommendations.</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-bold text-gray-900 mb-2">Free Certifications</h4>
                        <p class="text-gray-600 text-sm">Earn professional certificates for completed courses and exams at no additional cost.</p>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-bold text-gray-900 mb-2">Content Creation</h4>
                        <p class="text-gray-600 text-sm">Create and share your own blog posts to build your professional presence and expertise.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample Courses -->
        <div class="mb-20">
            <h2 class="text-3xl font-bold text-center text-gray-900 mb-4">Sample Course Pricing</h2>
            <p class="text-center text-gray-600 mb-12">
                Kursus dijual dengan harga tetap yang ditetapkan tutor (minimum IDR 30,000). Beli sekali, akses selamanya!
            </p>

            <?php if($sampleCourses->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php $__currentLoopData = $sampleCourses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200">
                    <?php if($course->thumbnail): ?>
                    <img src="<?php echo e(asset('storage/' . $course->thumbnail)); ?>"
                         alt="<?php echo e($course->title); ?>"
                         class="w-full h-48 object-cover">
                    <?php else: ?>
                    <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                        <span class="text-gray-500">No Image</span>
                    </div>
                    <?php endif; ?>

                    <div class="p-6">
                        <h4 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2"><?php echo e($course->title); ?></h4>
                        <p class="text-gray-600 text-sm mb-3">by <?php echo e($course->tutor->name); ?></p>

                        <div class="flex items-center justify-between mb-4">
                            <span class="text-2xl font-bold text-emerald-600">
                                <?php echo e($course->formatted_price); ?>

                            </span>
                            <span class="text-sm text-gray-500"><?php echo e($course->level_indonesian); ?></span>
                        </div>

                        <div class="space-y-2 mb-4">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L10 9.586V6z" clip-rule="evenodd"></path>
                                </svg>
                                <?php echo e($course->duration ?? 'Flexible'); ?>

                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Akses selamanya
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                </svg>
                                Platform fee 5%
                            </div>
                        </div>

                        <a href="<?php echo e(route('course.show', $course)); ?>"
                           class="block w-full bg-emerald-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-200 text-center text-sm">
                            Lihat Kursus
                        </a>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php else: ?>
            <div class="text-center py-12">
                <p class="text-gray-600">Belum ada kursus tersedia. Segera hadir!</p>
            </div>
            <?php endif; ?>
        </div>

        <!-- Certifications Info -->
        <div class="mb-20">
            <h2 class="text-3xl font-bold text-center text-gray-900 mb-12">Sertifikat & Membership Benefits</h2>

            <!-- Course Certifications -->
            <div class="mb-12">
                <h3 class="text-2xl font-bold text-center text-gray-900 mb-4">Free Course Certifications</h3>
                <p class="text-center text-gray-600 mb-8">
                    Sertifikat untuk kursus gratis sudah termasuk dalam NALA Membership
                </p>

                <div class="bg-emerald-50 border border-emerald-200 rounded-xl p-8 text-center">
                    <div class="max-w-2xl mx-auto">
                        <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <h4 class="text-xl font-bold text-emerald-900 mb-3">Sertifikat Gratis dengan NALA Membership</h4>
                        <p class="text-emerald-800 mb-6">
                            Dapatkan sertifikat untuk semua kursus gratis yang Anda selesaikan dengan berlangganan NALA Membership.
                            Tidak ada biaya tambahan - sertifikat sudah termasuk dalam paket membership Anda!
                        </p>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="bg-white rounded-lg p-4 border border-emerald-200">
                                <div class="text-emerald-600 font-semibold mb-1">Basic & Above</div>
                                <div class="text-sm text-gray-700">Sertifikat digital standar</div>
                            </div>
                            <div class="bg-white rounded-lg p-4 border border-emerald-200">
                                <div class="text-emerald-600 font-semibold mb-1">Standard & Above</div>
                                <div class="text-sm text-gray-700">Sertifikat dengan career path insights</div>
                            </div>
                            <div class="bg-white rounded-lg p-4 border border-emerald-200">
                                <div class="text-emerald-600 font-semibold mb-1">Pro</div>
                                <div class="text-sm text-gray-700">Sertifikat premium + AI recommendations</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tryout Exam Certifications -->
            <div class="mb-12">
                <h3 class="text-2xl font-bold text-center text-gray-900 mb-4">Tryout Exam Certificates</h3>
                <p class="text-center text-gray-600 mb-8">
                    Sertifikat ujian tryout tersedia dengan NALA Membership (Basic ke atas)
                </p>

                <div class="bg-blue-50 border border-blue-200 rounded-xl p-8">
                    <div class="max-w-4xl mx-auto">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h4 class="text-xl font-bold text-blue-900 mb-6 text-center">Tryout Exam Certificate Benefits</h4>

                        <div class="overflow-x-auto">
                            <table class="w-full bg-white rounded-lg shadow-sm">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Feature</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Free</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Basic<br><span class="text-emerald-600 font-semibold">(IDR 89,000/month)</span></th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Standard<br><span class="text-emerald-600 font-semibold">(IDR 129,000/month)</span></th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Pro<br><span class="text-emerald-600 font-semibold">(IDR 179,000/month)</span></th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Tryout Exam Certificates</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="text-red-500 font-bold">✗</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="text-emerald-500 font-bold">✓</div>
                                            <div class="text-xs text-gray-600">(Standard)</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="text-emerald-500 font-bold">✓</div>
                                            <div class="text-xs text-gray-600">(Career Insights)</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="text-emerald-500 font-bold">✓</div>
                                            <div class="text-xs text-gray-600">(Premium + AI Recommendations)</div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Blog Access & Creation</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="text-red-500 font-bold">✗</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="text-emerald-500 font-bold">✓</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="text-emerald-500 font-bold">✓</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="text-emerald-500 font-bold">✓</div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6 text-center">
                            <p class="text-blue-800 mb-4">
                                <strong>Catatan:</strong> Anda dapat mengikuti ujian tryout gratis atau berbayar, namun untuk mendapatkan sertifikat resmi,
                                diperlukan NALA Membership Basic ke atas.
                            </p>
                            <a href="#nala-membership"
                               class="inline-block bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200">
                                Upgrade ke NALA Membership
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Platform Fee Notice -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-4 text-center">Informasi Biaya Platform</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Course Revenue -->
                <div class="bg-white rounded-lg p-4 border border-blue-200">
                    <h4 class="font-semibold text-blue-900 mb-2">📚 Kursus</h4>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• Platform fee: 5% dari harga kursus</li>
                        <li>• Tutor: 60% (tanpa referral) atau 80% (dengan referral)</li>
                        <li>• Harga minimum: IDR 30.000</li>
                        <li>• Akses selamanya setelah pembelian</li>
                    </ul>
                </div>

                <!-- Exam Revenue -->
                <div class="bg-white rounded-lg p-4 border border-blue-200">
                    <h4 class="font-semibold text-blue-900 mb-2">📝 Ujian Tryout</h4>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• Tutor: 80% dari harga ujian (fixed rate)</li>
                        <li>• Platform: 20% (no referral system)</li>
                        <li>• Harga minimum: IDR 15.000</li>
                        <li>• Sertifikat perlu NALA Membership</li>
                    </ul>
                </div>
            </div>
            <p class="text-sm text-blue-700 text-center mt-4">
                Semua harga sudah termasuk pajak yang berlaku.
            </p>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/payment/pricing.blade.php ENDPATH**/ ?>