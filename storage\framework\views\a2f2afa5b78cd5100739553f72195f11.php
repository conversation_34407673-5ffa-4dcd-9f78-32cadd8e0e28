<?php $__env->startSection('title', 'Ujian Professional - Ngambiskuy'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-orange-50 via-white to-orange-100 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="inline-flex items-center px-4 py-2 rounded-full bg-primary/10 text-primary text-sm font-medium mb-6">
                🎯 Sistem Ujian Professional
            </div>
            <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                <PERSON><PERSON><PERSON>
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-primary to-orange-500">
                    Professional
                </span>
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                <PERSON>ji kemampuan Anda dengan ujian sertifikasi professional yang dirancang oleh para ahli industri
            </p>
            
            <!-- Quick Stats -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <div class="text-2xl font-bold text-primary"><?php echo e($stats['total_exams'] ?? 0); ?>+</div>
                    <div class="text-sm text-gray-600">Ujian Tersedia</div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <div class="text-2xl font-bold text-green-600"><?php echo e($stats['total_participants'] ?? 0); ?>+</div>
                    <div class="text-sm text-gray-600">Peserta</div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <div class="text-2xl font-bold text-blue-600"><?php echo e($stats['certificates_issued'] ?? 0); ?>+</div>
                    <div class="text-sm text-gray-600">Sertifikat</div>
                </div>
                <div class="bg-white rounded-xl p-6 shadow-sm">
                    <div class="text-2xl font-bold text-purple-600"><?php echo e($stats['success_rate'] ?? 85); ?>%</div>
                    <div class="text-sm text-gray-600">Tingkat Kelulusan</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filters Section -->
<div class="bg-white shadow-sm border-b border-orange-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <form method="GET" action="<?php echo e(route('exams.index')); ?>" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Cari Ujian</label>
                    <div class="relative">
                        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                               class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                               placeholder="Nama ujian atau deskripsi...">
                    </div>
                </div>

                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Kategori</label>
                    <select name="category" id="category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Semua Kategori</option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                <?php echo e($category->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Difficulty -->
                <div>
                    <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-1">Tingkat Kesulitan</label>
                    <select name="difficulty" id="difficulty" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Semua Tingkat</option>
                        <option value="beginner" <?php echo e(request('difficulty') == 'beginner' ? 'selected' : ''); ?>>Pemula</option>
                        <option value="intermediate" <?php echo e(request('difficulty') == 'intermediate' ? 'selected' : ''); ?>>Menengah</option>
                        <option value="advanced" <?php echo e(request('difficulty') == 'advanced' ? 'selected' : ''); ?>>Lanjutan</option>
                    </select>
                </div>

                <!-- Price Filter -->
                <div>
                    <label for="price_filter" class="block text-sm font-medium text-gray-700 mb-1">Harga</label>
                    <select name="price_filter" id="price_filter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="">Semua Harga</option>
                        <option value="free" <?php echo e(request('price_filter') == 'free' ? 'selected' : ''); ?>>Gratis</option>
                        <option value="paid" <?php echo e(request('price_filter') == 'paid' ? 'selected' : ''); ?>>Berbayar</option>
                    </select>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-3">
                <button type="submit" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Cari Ujian
                </button>
                <?php if(request()->hasAny(['search', 'category', 'difficulty', 'price_filter'])): ?>
                    <a href="<?php echo e(route('exams.index')); ?>" class="btn btn-outline">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Reset Filter
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>
</div>

<!-- Exams Grid -->
<section class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php if($exams->count() > 0): ?>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100">
                        <!-- Exam Header -->
                        <div class="p-6 border-b border-gray-100">
                            <div class="flex justify-between items-start mb-4">
                                <?php if($exam->price == 0): ?>
                                    <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full font-medium">GRATIS</span>
                                <?php else: ?>
                                    <span class="bg-primary/10 text-primary text-xs px-3 py-1 rounded-full font-medium">PREMIUM</span>
                                <?php endif; ?>
                                <span class="text-gray-500 text-sm"><?php echo e($exam->questions_count); ?> soal</span>
                            </div>
                            
                            <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2"><?php echo e($exam->title); ?></h3>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-2"><?php echo e($exam->description); ?></p>
                            
                            <?php if($exam->category): ?>
                                <span class="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"><?php echo e($exam->category->name); ?></span>
                            <?php endif; ?>
                        </div>

                        <!-- Exam Details -->
                        <div class="p-6">
                            <div class="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-6">
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span><?php echo e($exam->time_limit); ?> menit</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span><?php echo e($exam->passing_score); ?>% lulus</span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    <span><?php echo e(ucfirst($exam->difficulty_level)); ?></span>
                                </div>
                                <div class="flex items-center gap-2">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    <span><?php echo e($exam->enrollments_count); ?> peserta</span>
                                </div>
                            </div>

                            <!-- Price and Action -->
                            <div class="flex items-center justify-between">
                                <div>
                                    <?php if($exam->price == 0): ?>
                                        <span class="text-2xl font-bold text-green-600">GRATIS</span>
                                    <?php else: ?>
                                        <span class="text-2xl font-bold text-gray-900">Rp <?php echo e(number_format($exam->price, 0, ',', '.')); ?></span>
                                    <?php endif; ?>
                                </div>
                                <a href="<?php echo e(route('exams.show', $exam)); ?>" class="btn btn-primary">
                                    <?php echo e($exam->price == 0 ? 'Mulai Gratis' : 'Daftar Ujian'); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <?php if($exams->hasPages()): ?>
                <div class="mt-12">
                    <?php echo e($exams->links()); ?>

                </div>
            <?php endif; ?>
        <?php else: ?>
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Ujian</h3>
                <p class="text-gray-600 mb-6">Tidak ada ujian yang sesuai dengan kriteria pencarian Anda.</p>
                <a href="<?php echo e(route('exams.index')); ?>" class="btn btn-primary">
                    Lihat Semua Ujian
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/exams-v2/index.blade.php ENDPATH**/ ?>