<?php $__env->startSection('title', 'Ujian - Ngambiskuy'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white">
        <div class="container mx-auto px-4 py-16">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4"><PERSON><PERSON><PERSON></h1>
                <p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
                    Uji kemampuan Anda dengan berbagai ujian sertifikasi dari tutor terbaik
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg px-6 py-3">
                        <span class="text-2xl font-bold"><?php echo e($exams->total()); ?></span>
                        <span class="text-blue-100 ml-2">Ujian Tersedia</span>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg px-6 py-3">
                        <span class="text-2xl font-bold"><?php echo e($categories->count()); ?></span>
                        <span class="text-blue-100 ml-2">Kategori</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-6">
            <form method="GET" action="<?php echo e(route('exams.index')); ?>" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Cari Ujian</label>
                        <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Nama ujian atau deskripsi...">
                    </div>

                    <!-- Category Filter -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Kategori</label>
                        <select name="category" id="category"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Semua Kategori</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Difficulty Filter -->
                    <div>
                        <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-1">Tingkat Kesulitan</label>
                        <select name="difficulty" id="difficulty"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Semua Tingkat</option>
                            <option value="beginner" <?php echo e(request('difficulty') == 'beginner' ? 'selected' : ''); ?>>Pemula</option>
                            <option value="intermediate" <?php echo e(request('difficulty') == 'intermediate' ? 'selected' : ''); ?>>Menengah</option>
                            <option value="advanced" <?php echo e(request('difficulty') == 'advanced' ? 'selected' : ''); ?>>Lanjutan</option>
                        </select>
                    </div>

                    <!-- Price Filter -->
                    <div>
                        <label for="price_filter" class="block text-sm font-medium text-gray-700 mb-1">Harga</label>
                        <select name="price_filter" id="price_filter"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Semua Harga</option>
                            <option value="free" <?php echo e(request('price_filter') == 'free' ? 'selected' : ''); ?>>Gratis</option>
                            <option value="paid" <?php echo e(request('price_filter') == 'paid' ? 'selected' : ''); ?>>Berbayar</option>
                            <option value="under_50k" <?php echo e(request('price_filter') == 'under_50k' ? 'selected' : ''); ?>>Di bawah Rp 50.000</option>
                            <option value="under_100k" <?php echo e(request('price_filter') == 'under_100k' ? 'selected' : ''); ?>>Di bawah Rp 100.000</option>
                            <option value="over_100k" <?php echo e(request('price_filter') == 'over_100k' ? 'selected' : ''); ?>>Di atas Rp 100.000</option>
                        </select>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <button type="submit" class="btn bg-blue-600 hover:bg-blue-700 text-white">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Cari Ujian
                    </button>
                    <?php if(request()->hasAny(['search', 'category', 'difficulty', 'price_filter'])): ?>
                        <a href="<?php echo e(route('exams.index')); ?>" class="btn btn-outline">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Reset Filter
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Exams Grid -->
    <div class="container mx-auto px-4 py-8">
        <?php if($exams->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <?php $__currentLoopData = $exams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $exam): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-200 overflow-hidden">
                        <!-- Exam Header -->
                        <div class="p-6">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                        <a href="<?php echo e(route('exams.show', $exam)); ?>" class="hover:text-blue-600 transition-colors">
                                            <?php echo e($exam->title); ?>

                                        </a>
                                    </h3>
                                    <p class="text-gray-600 text-sm line-clamp-3 mb-3"><?php echo e($exam->description); ?></p>
                                </div>
                            </div>

                            <!-- Exam Meta -->
                            <div class="flex flex-wrap gap-2 mb-4">
                                <?php if($exam->category): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <?php echo e($exam->category->name); ?>

                                    </span>
                                <?php endif; ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <?php echo e($exam->difficulty_label); ?>

                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <?php echo e($exam->questions->count()); ?> soal
                                </span>
                            </div>

                            <!-- Exam Stats -->
                            <div class="grid grid-cols-2 gap-4 mb-4 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <?php echo e($exam->time_limit); ?> menit
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                                    </svg>
                                    <?php echo e($exam->passing_score); ?>% lulus
                                </div>
                            </div>

                            <!-- Tutor Info -->
                            <div class="flex items-center mb-4">
                                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-medium text-gray-600">
                                        <?php echo e(substr($exam->tutor->name, 0, 1)); ?>

                                    </span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900"><?php echo e($exam->tutor->name); ?></p>
                                    <p class="text-xs text-gray-500">Tutor</p>
                                </div>
                            </div>
                        </div>

                        <!-- Exam Footer -->
                        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <span class="text-2xl font-bold text-gray-900"><?php echo e($exam->formatted_price); ?></span>
                                </div>
                                <a href="<?php echo e(route('exams.show', $exam)); ?>" class="btn bg-blue-600 hover:bg-blue-700 text-white">
                                    Lihat Detail
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="flex justify-center">
                <?php echo e($exams->withQueryString()->links()); ?>

            </div>
        <?php else: ?>
            <!-- No Exams Found -->
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-medium text-gray-900 mb-2">Tidak ada ujian ditemukan</h3>
                <p class="text-gray-600 mb-6">Coba ubah filter pencarian atau kembali lagi nanti.</p>
                <a href="<?php echo e(route('exams.index')); ?>" class="btn bg-blue-600 hover:bg-blue-700 text-white">
                    Lihat Semua Ujian
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/exams/index.blade.php ENDPATH**/ ?>