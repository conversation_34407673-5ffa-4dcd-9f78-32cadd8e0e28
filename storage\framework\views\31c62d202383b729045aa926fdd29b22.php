<?php $__env->startSection('title', $exam->title . ' - <PERSON><PERSON>an Professional'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100">
    <!-- Breadcrumb -->
    <div class="bg-white border-b border-orange-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="<?php echo e(route('home')); ?>" class="text-gray-700 hover:text-primary">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                            </svg>
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <a href="<?php echo e(route('exams.index')); ?>" class="ml-1 text-gray-700 hover:text-primary md:ml-2">Ujian</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="ml-1 text-gray-500 md:ml-2"><?php echo e($exam->title); ?></span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Exam Header -->
                <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-100">
                    <div class="flex items-start justify-between mb-6">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-4">
                                <?php if($exam->price == 0): ?>
                                    <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full font-medium">GRATIS</span>
                                <?php else: ?>
                                    <span class="bg-primary/10 text-primary text-sm px-3 py-1 rounded-full font-medium">PREMIUM</span>
                                <?php endif; ?>
                                <?php if($exam->category): ?>
                                    <span class="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full"><?php echo e($exam->category->name); ?></span>
                                <?php endif; ?>
                            </div>
                            <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4"><?php echo e($exam->title); ?></h1>
                            <p class="text-lg text-gray-600 leading-relaxed"><?php echo e($exam->description); ?></p>
                        </div>
                    </div>

                    <!-- Exam Stats -->
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 p-6 bg-gray-50 rounded-xl">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary"><?php echo e($stats['total_questions']); ?></div>
                            <div class="text-sm text-gray-600">Soal</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600"><?php echo e($exam->time_limit); ?></div>
                            <div class="text-sm text-gray-600">Menit</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600"><?php echo e($exam->passing_score); ?>%</div>
                            <div class="text-sm text-gray-600">Batas Lulus</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600"><?php echo e($stats['total_enrollments']); ?></div>
                            <div class="text-sm text-gray-600">Peserta</div>
                        </div>
                    </div>
                </div>

                <!-- Exam Details -->
                <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-100">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Detail Ujian</h2>
                    
                    <div class="space-y-6">
                        <!-- What You'll Learn -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Yang Akan Diuji</h3>
                            <div class="prose prose-gray max-w-none">
                                <p class="text-gray-600"><?php echo e($exam->description); ?></p>
                            </div>
                        </div>

                        <!-- Requirements -->
                        <?php if($exam->requirements): ?>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Persyaratan</h3>
                            <div class="prose prose-gray max-w-none">
                                <p class="text-gray-600"><?php echo e($exam->requirements); ?></p>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Exam Rules -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Aturan Ujian</h3>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <ul class="space-y-2 text-sm text-blue-800">
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Waktu ujian: <?php echo e($exam->time_limit); ?> menit
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Batas kelulusan: <?php echo e($exam->passing_score); ?>%
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Maksimal <?php echo e($exam->max_attempts); ?> kali percobaan
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Jawaban tersimpan otomatis
                                    </li>
                                    <?php if($exam->certificate_enabled): ?>
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Sertifikat digital untuk yang lulus
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tutor Info -->
                <?php if($exam->tutor): ?>
                <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-100">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Pembuat Ujian</h2>
                    <div class="flex items-center gap-4">
                        <img src="<?php echo e($exam->tutor->profile_picture ? asset('storage/' . $exam->tutor->profile_picture) : asset('images/avatars/placeholder.svg')); ?>" 
                             alt="<?php echo e($exam->tutor->name); ?>" 
                             class="w-16 h-16 rounded-full object-cover">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900"><?php echo e($exam->tutor->name); ?></h3>
                            <p class="text-gray-600"><?php echo e($exam->tutor->title ?? 'Professional Instructor'); ?></p>
                            <?php if($exam->tutor->bio): ?>
                                <p class="text-sm text-gray-500 mt-1"><?php echo e(Str::limit($exam->tutor->bio, 100)); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Enrollment Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 border border-orange-100 sticky top-6">
                    <div class="text-center mb-6">
                        <?php if($exam->price == 0): ?>
                            <div class="text-3xl font-bold text-green-600 mb-2">GRATIS</div>
                            <p class="text-gray-600">Ujian gratis untuk semua</p>
                        <?php else: ?>
                            <div class="text-3xl font-bold text-gray-900 mb-2">Rp <?php echo e(number_format($exam->price, 0, ',', '.')); ?></div>
                            <p class="text-gray-600">Investasi untuk sertifikasi</p>
                        <?php endif; ?>
                    </div>

                    <!-- Status Message -->
                    <?php if($statusMessage): ?>
                        <div class="mb-4 p-4 rounded-lg border <?php echo e($examStatus === 'can_take' ? 'bg-green-50 border-green-200 text-green-800' :
                            ($examStatus === 'has_active_attempt' ? 'bg-blue-50 border-blue-200 text-blue-800' :
                            ($examStatus === 'max_attempts_reached' ? 'bg-red-50 border-red-200 text-red-800' :
                            ($examStatus === 'payment_pending' ? 'bg-yellow-50 border-yellow-200 text-yellow-800' :
                            'bg-gray-50 border-gray-200 text-gray-800')))); ?>">
                            <div class="flex items-start gap-3">
                                <?php if($examStatus === 'can_take'): ?>
                                    <svg class="w-5 h-5 mt-0.5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                <?php elseif($examStatus === 'has_active_attempt'): ?>
                                    <svg class="w-5 h-5 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                <?php elseif($examStatus === 'max_attempts_reached'): ?>
                                    <svg class="w-5 h-5 mt-0.5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                <?php elseif($examStatus === 'payment_pending'): ?>
                                    <svg class="w-5 h-5 mt-0.5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                <?php else: ?>
                                    <svg class="w-5 h-5 mt-0.5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                <?php endif; ?>
                                <div>
                                    <p class="font-medium"><?php echo e($statusMessage); ?></p>
                                    <?php if($examStatus === 'max_attempts_reached' && $userAttempts->count() > 0): ?>
                                        <p class="text-sm mt-1">
                                            Percobaan terakhir: <?php echo e($userAttempts->first()->created_at->format('d M Y, H:i')); ?>

                                            <?php if($userAttempts->first()->is_passed): ?>
                                                <span class="text-green-600 font-medium">(Lulus - <?php echo e(number_format($userAttempts->first()->score_percentage, 1)); ?>%)</span>
                                            <?php else: ?>
                                                <span class="text-red-600 font-medium">(Tidak Lulus - <?php echo e(number_format($userAttempts->first()->score_percentage, 1)); ?>%)</span>
                                            <?php endif; ?>
                                        </p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Action Buttons -->
                    <?php if(auth()->guard()->check()): ?>
                        <?php if($examStatus === 'has_active_attempt'): ?>
                            <a href="<?php echo e(route('exams.take', $exam)); ?>"
                               class="w-full bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4 shadow-lg">
                                🔄 Lanjutkan Ujian
                            </a>
                        <?php elseif($examStatus === 'can_take'): ?>
                            <a href="<?php echo e(route('exams.take', $exam)); ?>"
                               class="w-full bg-primary hover:bg-primary/90 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4 shadow-lg">
                                🚀 Mulai Ujian Professional
                            </a>
                        <?php elseif($examStatus === 'not_enrolled'): ?>
                            <form action="<?php echo e(route('exams.enroll', $exam)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <button type="submit"
                                        class="w-full bg-primary hover:bg-primary/90 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-200 mb-4 shadow-lg">
                                    <?php echo e($exam->price == 0 ? '📝 Daftar Gratis' : '💳 Daftar Ujian'); ?>

                                </button>
                            </form>
                        <?php elseif($examStatus === 'payment_pending'): ?>
                            <button disabled
                                    class="w-full bg-gray-400 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4 cursor-not-allowed">
                                💳 Menunggu Pembayaran
                            </button>
                        <?php elseif($examStatus === 'max_attempts_reached'): ?>
                            <button disabled
                                    class="w-full bg-gray-400 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4 cursor-not-allowed">
                                ❌ Batas Percobaan Tercapai
                            </button>
                        <?php endif; ?>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>"
                           class="w-full bg-primary hover:bg-primary/90 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4 shadow-lg">
                            🔐 Login untuk Mengikuti Ujian
                        </a>
                    <?php endif; ?>

                    <!-- Exam Features -->
                    <div class="space-y-3 text-sm">
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span><?php echo e($stats['total_questions']); ?> soal berkualitas</span>
                        </div>
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Auto-save jawaban</span>
                        </div>
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Hasil langsung</span>
                        </div>
                        <?php if($exam->certificate_enabled): ?>
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Sertifikat digital</span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Info -->
                <div class="bg-white rounded-2xl shadow-lg p-6 border border-orange-100">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Ujian</h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tingkat:</span>
                            <span class="font-medium text-gray-900"><?php echo e(ucfirst($exam->difficulty_level)); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Durasi:</span>
                            <span class="font-medium text-gray-900"><?php echo e($exam->time_limit); ?> menit</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Percobaan:</span>
                            <span class="font-medium text-gray-900"><?php echo e($exam->max_attempts); ?>x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Peserta:</span>
                            <span class="font-medium text-gray-900"><?php echo e($stats['total_enrollments']); ?></span>
                        </div>
                        <?php if($stats['average_score'] > 0): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Rata-rata Skor:</span>
                            <span class="font-medium text-gray-900"><?php echo e(number_format($stats['average_score'], 1)); ?>%</span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- User Attempt History -->
                <?php if(auth()->guard()->check()): ?>
                    <?php if($userAttempts->count() > 0): ?>
                        <div class="bg-white rounded-2xl shadow-lg p-6 border border-orange-100">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Riwayat Percobaan Anda</h3>
                            <div class="space-y-3">
                                <?php $__currentLoopData = $userAttempts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attempt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                        <div class="flex items-center gap-3">
                                            <div class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold <?php echo e($attempt->is_passed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                                <?php echo e($attempt->attempt_number); ?>

                                            </div>
                                            <div>
                                                <div class="font-medium text-gray-900">
                                                    Percobaan <?php echo e($attempt->attempt_number); ?>

                                                    <?php if($attempt->status === 'in_progress'): ?>
                                                        <span class="text-blue-600 text-sm">(Sedang Berlangsung)</span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="text-sm text-gray-600">
                                                    <?php echo e($attempt->created_at->format('d M Y, H:i')); ?>

                                                    <?php if($attempt->completed_at): ?>
                                                        - Selesai: <?php echo e($attempt->completed_at->format('H:i')); ?>

                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <?php if($attempt->status === 'completed'): ?>
                                                <div class="text-lg font-bold <?php echo e($attempt->is_passed ? 'text-green-600' : 'text-red-600'); ?>">
                                                    <?php echo e(number_format($attempt->score_percentage, 1)); ?>%
                                                </div>
                                                <div class="text-sm text-gray-600">
                                                    <?php echo e($attempt->is_passed ? 'Lulus' : 'Tidak Lulus'); ?>

                                                </div>
                                            <?php elseif($attempt->status === 'in_progress'): ?>
                                                <div class="text-sm text-blue-600 font-medium">
                                                    Berlangsung
                                                </div>
                                            <?php elseif($attempt->status === 'expired'): ?>
                                                <div class="text-sm text-orange-600 font-medium">
                                                    Waktu Habis
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="text-sm text-blue-800">
                                    <strong>Percobaan:</strong> <?php echo e($userAttempts->count()); ?> dari <?php echo e($exam->max_attempts); ?> kali
                                    <?php if($userAttempts->where('is_passed', true)->count() > 0): ?>
                                        <br><strong>Status:</strong> <span class="text-green-600 font-medium">Sudah Lulus</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/exams-v2/show.blade.php ENDPATH**/ ?>