@extends('layouts.app')

@section('title', $exam->title . ' - <PERSON><PERSON>an <PERSON>')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-100">
    <!-- Breadcrumb -->
    <div class="bg-white border-b border-orange-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <nav class="flex" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="text-gray-700 hover:text-primary">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                            </svg>
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ route('exams.index') }}" class="ml-1 text-gray-700 hover:text-primary md:ml-2">Ujian</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="ml-1 text-gray-500 md:ml-2">{{ $exam->title }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Exam Header -->
                <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-100">
                    <div class="flex items-start justify-between mb-6">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-4">
                                @if($exam->price == 0)
                                    <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full font-medium">GRATIS</span>
                                @else
                                    <span class="bg-primary/10 text-primary text-sm px-3 py-1 rounded-full font-medium">PREMIUM</span>
                                @endif
                                @if($exam->category)
                                    <span class="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded-full">{{ $exam->category->name }}</span>
                                @endif
                            </div>
                            <h1 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">{{ $exam->title }}</h1>
                            <p class="text-lg text-gray-600 leading-relaxed">{{ $exam->description }}</p>
                        </div>
                    </div>

                    <!-- Exam Stats -->
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 p-6 bg-gray-50 rounded-xl">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary">{{ $stats['total_questions'] }}</div>
                            <div class="text-sm text-gray-600">Soal</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ $exam->time_limit }}</div>
                            <div class="text-sm text-gray-600">Menit</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">{{ $exam->passing_score }}%</div>
                            <div class="text-sm text-gray-600">Batas Lulus</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">{{ $stats['total_enrollments'] }}</div>
                            <div class="text-sm text-gray-600">Peserta</div>
                        </div>
                    </div>
                </div>

                <!-- Exam Details -->
                <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-100">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Detail Ujian</h2>
                    
                    <div class="space-y-6">
                        <!-- What You'll Learn -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Yang Akan Diuji</h3>
                            <div class="prose prose-gray max-w-none">
                                <p class="text-gray-600">{{ $exam->description }}</p>
                            </div>
                        </div>

                        <!-- Requirements -->
                        @if($exam->requirements)
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Persyaratan</h3>
                            <div class="prose prose-gray max-w-none">
                                <p class="text-gray-600">{{ $exam->requirements }}</p>
                            </div>
                        </div>
                        @endif

                        <!-- Exam Rules -->
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Aturan Ujian</h3>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <ul class="space-y-2 text-sm text-blue-800">
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Waktu ujian: {{ $exam->time_limit }} menit
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Batas kelulusan: {{ $exam->passing_score }}%
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Maksimal {{ $exam->max_attempts }} kali percobaan
                                    </li>
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Jawaban tersimpan otomatis
                                    </li>
                                    @if($exam->certificate_enabled)
                                    <li class="flex items-start gap-2">
                                        <svg class="w-4 h-4 mt-0.5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        Sertifikat digital untuk yang lulus
                                    </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tutor Info -->
                @if($exam->tutor)
                <div class="bg-white rounded-2xl shadow-lg p-8 border border-orange-100">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Pembuat Ujian</h2>
                    <div class="flex items-center gap-4">
                        <img src="{{ $exam->tutor->profile_picture ? asset('storage/' . $exam->tutor->profile_picture) : asset('images/avatars/placeholder.svg') }}" 
                             alt="{{ $exam->tutor->name }}" 
                             class="w-16 h-16 rounded-full object-cover">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">{{ $exam->tutor->name }}</h3>
                            <p class="text-gray-600">{{ $exam->tutor->title ?? 'Professional Instructor' }}</p>
                            @if($exam->tutor->bio)
                                <p class="text-sm text-gray-500 mt-1">{{ Str::limit($exam->tutor->bio, 100) }}</p>
                            @endif
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Enrollment Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 border border-orange-100 sticky top-6">
                    <div class="text-center mb-6">
                        @if($exam->price == 0)
                            <div class="text-3xl font-bold text-green-600 mb-2">GRATIS</div>
                            <p class="text-gray-600">Ujian gratis untuk semua</p>
                        @else
                            <div class="text-3xl font-bold text-gray-900 mb-2">Rp {{ number_format($exam->price, 0, ',', '.') }}</div>
                            <p class="text-gray-600">Investasi untuk sertifikasi</p>
                        @endif
                    </div>

                    @auth
                        @php
                            $enrollment = $exam->enrollments()->where('user_id', auth()->id())->first();
                            $hasActiveAttempt = $exam->attempts()->where('user_id', auth()->id())->where('status', 'in_progress')->exists();
                        @endphp

                        @if($enrollment && $enrollment->payment_status === 'paid')
                            @if($hasActiveAttempt)
                                <a href="{{ route('exams.take', $exam) }}" 
                                   class="w-full bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4">
                                    🔄 Lanjutkan Ujian
                                </a>
                            @else
                                <a href="{{ route('exams.take', $exam) }}" 
                                   class="w-full bg-primary hover:bg-primary/90 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4">
                                    🚀 Mulai Ujian Professional
                                </a>
                            @endif
                        @else
                            <form action="{{ route('exams.enroll', $exam) }}" method="POST">
                                @csrf
                                <button type="submit" 
                                        class="w-full bg-primary hover:bg-primary/90 text-white py-4 px-6 rounded-xl font-semibold transition-all duration-200 mb-4">
                                    {{ $exam->price == 0 ? '📝 Daftar Gratis' : '💳 Daftar Ujian' }}
                                </button>
                            </form>
                        @endif
                    @else
                        <a href="{{ route('login') }}" 
                           class="w-full bg-primary hover:bg-primary/90 text-white py-4 px-6 rounded-xl font-semibold text-center block transition-all duration-200 mb-4">
                            🔐 Login untuk Mengikuti Ujian
                        </a>
                    @endauth

                    <!-- Exam Features -->
                    <div class="space-y-3 text-sm">
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>{{ $stats['total_questions'] }} soal berkualitas</span>
                        </div>
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Auto-save jawaban</span>
                        </div>
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Hasil langsung</span>
                        </div>
                        @if($exam->certificate_enabled)
                        <div class="flex items-center gap-3 text-gray-600">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Sertifikat digital</span>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Quick Info -->
                <div class="bg-white rounded-2xl shadow-lg p-6 border border-orange-100">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Ujian</h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tingkat:</span>
                            <span class="font-medium text-gray-900">{{ ucfirst($exam->difficulty_level) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Durasi:</span>
                            <span class="font-medium text-gray-900">{{ $exam->time_limit }} menit</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Percobaan:</span>
                            <span class="font-medium text-gray-900">{{ $exam->max_attempts }}x</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Peserta:</span>
                            <span class="font-medium text-gray-900">{{ $stats['total_enrollments'] }}</span>
                        </div>
                        @if($stats['average_score'] > 0)
                        <div class="flex justify-between">
                            <span class="text-gray-600">Rata-rata Skor:</span>
                            <span class="font-medium text-gray-900">{{ number_format($stats['average_score'], 1) }}%</span>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
