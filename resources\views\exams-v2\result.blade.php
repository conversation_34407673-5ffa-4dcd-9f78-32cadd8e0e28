@extends('layouts.app')

@section('title', '<PERSON><PERSON>: ' . $exam->title . ' - Ngambiskuy')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- Header -->
    <div class="bg-white shadow-lg border-b-2 border-blue-100">
        <div class="max-w-7xl mx-auto px-6 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                        <span class="text-white font-bold text-xl">N</span>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Hasil <PERSON></h1>
                        <p class="text-gray-600">{{ $exam->title }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="{{ route('exams.show', $exam) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200">
                        Kembali ke Ujian
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-8">
        <!-- Result Summary -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Score Card -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-lg p-8">
                    <div class="text-center mb-8">
                        @if($attempt->is_passed)
                            <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <h2 class="text-3xl font-bold text-green-600 mb-2">Selamat! Anda Lulus</h2>
                            <p class="text-gray-600">Anda telah berhasil menyelesaikan ujian dengan baik</p>
                        @else
                            <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </div>
                            <h2 class="text-3xl font-bold text-red-600 mb-2">Belum Lulus</h2>
                            <p class="text-gray-600">Jangan menyerah! Anda dapat mencoba lagi</p>
                        @endif
                    </div>

                    <!-- Score Display -->
                    <div class="text-center mb-8">
                        <div class="relative inline-block">
                            <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                                <circle cx="60" cy="60" r="50" stroke="#e5e7eb" stroke-width="8" fill="none"></circle>
                                <circle cx="60" cy="60" r="50" 
                                        stroke="{{ $attempt->is_passed ? '#10b981' : '#ef4444' }}" 
                                        stroke-width="8" 
                                        fill="none"
                                        stroke-dasharray="{{ 2 * pi() * 50 }}"
                                        stroke-dashoffset="{{ 2 * pi() * 50 * (1 - $attempt->score_percentage / 100) }}"
                                        class="transition-all duration-1000 ease-out"></circle>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center">
                                    <div class="text-3xl font-bold {{ $attempt->is_passed ? 'text-green-600' : 'text-red-600' }}">
                                        {{ number_format($attempt->score_percentage, 1) }}%
                                    </div>
                                    <div class="text-sm text-gray-500">Skor</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Score Details -->
                    <div class="grid grid-cols-2 gap-6">
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900">{{ $attempt->correct_answers }}</div>
                            <div class="text-sm text-gray-600">Jawaban Benar</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900">{{ $attempt->total_questions - $attempt->correct_answers }}</div>
                            <div class="text-sm text-gray-600">Jawaban Salah</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900">{{ $attempt->total_points }}</div>
                            <div class="text-sm text-gray-600">Total Poin</div>
                        </div>
                        <div class="text-center p-4 bg-gray-50 rounded-lg">
                            <div class="text-2xl font-bold text-gray-900">{{ gmdate('H:i:s', $attempt->time_taken) }}</div>
                            <div class="text-sm text-gray-600">Waktu Pengerjaan</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Exam Info -->
            <div class="space-y-6">
                <!-- Exam Details -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Detail Ujian</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Judul:</span>
                            <span class="font-medium text-gray-900">{{ $exam->title }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Kategori:</span>
                            <span class="font-medium text-gray-900">{{ $exam->category->name ?? 'Umum' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tingkat:</span>
                            <span class="font-medium text-gray-900">{{ ucfirst($exam->difficulty_level) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Batas Lulus:</span>
                            <span class="font-medium text-gray-900">{{ $exam->passing_score }}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Total Soal:</span>
                            <span class="font-medium text-gray-900">{{ $attempt->total_questions }}</span>
                        </div>
                    </div>
                </div>

                <!-- Attempt Info -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Info Percobaan</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Percobaan ke:</span>
                            <span class="font-medium text-gray-900">{{ $attempt->attempt_number }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Dimulai:</span>
                            <span class="font-medium text-gray-900">{{ $attempt->started_at->format('d/m/Y H:i') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Selesai:</span>
                            <span class="font-medium text-gray-900">{{ $attempt->completed_at->format('d/m/Y H:i') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Status:</span>
                            <span class="font-medium {{ $attempt->is_passed ? 'text-green-600' : 'text-red-600' }}">
                                {{ $attempt->is_passed ? 'Lulus' : 'Tidak Lulus' }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4">Tindakan</h3>
                    <div class="space-y-3">
                        @if(!$attempt->is_passed && $enrollment && $enrollment->attempts_used < $exam->max_attempts)
                            <a href="{{ route('exams-v2.take', $exam) }}" 
                               class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium text-center block transition-all duration-200">
                                Coba Lagi
                            </a>
                        @endif
                        
                        @if($attempt->is_passed && $exam->certificate_enabled)
                            <button class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-medium transition-all duration-200">
                                Download Sertifikat
                            </button>
                        @endif
                        
                        <a href="{{ route('exams.index') }}" 
                           class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium text-center block transition-all duration-200">
                            Lihat Ujian Lain
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Question Review (if enabled) -->
        @if($exam->show_results_immediately)
            <div class="bg-white rounded-xl shadow-lg p-8">
                <h3 class="text-xl font-bold text-gray-900 mb-6">Review Jawaban</h3>
                
                <div class="space-y-6">
                    @foreach($attempt->answers as $index => $answer)
                        <div class="border border-gray-200 rounded-lg p-6">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <span class="w-8 h-8 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-sm font-medium">
                                        {{ $index + 1 }}
                                    </span>
                                    <div>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full {{ $answer->is_correct ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $answer->is_correct ? 'Benar' : 'Salah' }}
                                        </span>
                                        <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full ml-2">
                                            {{ $answer->question->points }} poin
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2">{{ $answer->question->question }}</h4>
                            </div>
                            
                            @if($answer->question->type === 'multiple_choice' || $answer->question->type === 'true_false')
                                <div class="space-y-2">
                                    @foreach($answer->question->options as $option)
                                        <div class="flex items-center p-3 rounded-lg {{ $option->is_correct ? 'bg-green-50 border border-green-200' : ($answer->selected_option_id == $option->id ? 'bg-red-50 border border-red-200' : 'bg-gray-50') }}">
                                            <div class="flex items-center space-x-3">
                                                @if($answer->selected_option_id == $option->id)
                                                    <svg class="w-5 h-5 {{ $option->is_correct ? 'text-green-600' : 'text-red-600' }}" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                                    </svg>
                                                @elseif($option->is_correct)
                                                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                                    </svg>
                                                @else
                                                    <div class="w-5 h-5"></div>
                                                @endif
                                                <span class="text-gray-900">{{ $option->option_text }}</span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @elseif($answer->question->type === 'short_answer')
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-gray-900">{{ $answer->answer_text ?: 'Tidak dijawab' }}</p>
                                </div>
                            @endif
                            
                            @if($answer->question->explanation)
                                <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <h5 class="font-medium text-blue-900 mb-2">Penjelasan:</h5>
                                    <p class="text-blue-800">{{ $answer->question->explanation }}</p>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
