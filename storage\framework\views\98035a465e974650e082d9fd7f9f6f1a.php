<?php $__env->startSection('title', 'Ujian: ' . $exam->title . ' - Ngambiskuy'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- Professional Header -->
    <div class="bg-white shadow-lg border-b-2 border-blue-100 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <!-- Left Section -->
                <div class="flex items-center space-x-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                            <span class="text-white font-bold text-lg">N</span>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900"><?php echo e($exam->title); ?></h1>
                            <p class="text-sm text-gray-500">Per<PERSON>baan <?php echo e($attempt->attempt_number); ?> dari <?php echo e($exam->max_attempts); ?></p>
                        </div>
                    </div>
                </div>
                
                <!-- Center Section - Progress -->
                <div class="hidden md:flex items-center space-x-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" id="progressPercentage">0%</div>
                        <div class="text-xs text-gray-500">Selesai</div>
                    </div>
                    <div class="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div class="h-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-500" id="progressBar" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- Right Section - Timer & Submit -->
                <div class="flex items-center space-x-4">
                    <div class="bg-gradient-to-r from-red-50 to-orange-50 border-2 border-red-200 rounded-xl px-4 py-3 shadow-sm">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="font-mono text-lg font-bold text-red-700" id="timer">
                                <?php echo e(gmdate('H:i:s', $timeRemaining)); ?>

                            </span>
                        </div>
                    </div>
                    <button type="button" onclick="submitExam()" class="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-6 py-3 rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-105">
                        <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Selesai Ujian
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-6 py-8 page-transition" id="examContent">
        <form id="examForm" action="<?php echo e(route('exams.submit', $exam)); ?>" method="POST">
            <?php echo csrf_field(); ?>
            <input type="hidden" name="attempt_id" value="<?php echo e($attempt->id); ?>">

            <div class="grid grid-cols-1 xl:grid-cols-4 gap-8">
                <!-- Question Section -->
                <div class="xl:col-span-3">
                    <div class="question-card p-8">
                        <!-- Question Header -->
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                                    <?php echo e($currentQuestionIndex); ?>

                                </div>
                                <div>
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $currentQuestion->type))); ?>

                                        </span>
                                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                            <?php echo e($currentQuestion->points); ?> poin
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-500">Soal <?php echo e($currentQuestionIndex); ?> dari <?php echo e($questions->count()); ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- Question Text -->
                        <div class="mb-8">
                            <h2 class="text-xl font-semibold text-gray-900 leading-relaxed">
                                <?php echo e($currentQuestion->question); ?>

                            </h2>
                        </div>

                        <!-- Answer Options -->
                        <?php if($currentQuestion->type === 'multiple_choice' || $currentQuestion->type === 'true_false'): ?>
                            <div class="space-y-4">
                                <?php $__currentLoopData = $currentQuestion->options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $optionIndex => $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <label class="option-card p-5 block cursor-pointer <?php echo e(isset($existingAnswers[$currentQuestion->id]) && (string)$existingAnswers[$currentQuestion->id] === (string)$option->id ? 'selected' : ''); ?>"
                                           data-option-id="<?php echo e($option->id); ?>">
                                        <div class="flex items-start space-x-4">
                                            <input type="radio"
                                                   name="selected_option"
                                                   value="<?php echo e($option->id); ?>"
                                                   class="mt-1.5 h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300"
                                                   <?php echo e(isset($existingAnswers[$currentQuestion->id]) && (string)$existingAnswers[$currentQuestion->id] === (string)$option->id ? 'checked' : ''); ?>

                                                   onchange="handleAnswerChange(this)">
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-3">
                                                    <span class="option-letter w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-semibold text-gray-700 transition-all duration-300">
                                                        <?php echo e(chr(65 + $optionIndex)); ?>

                                                    </span>
                                                    <span class="text-gray-900 text-lg font-medium"><?php echo e($option->option_text); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php elseif($currentQuestion->type === 'short_answer'): ?>
                            <div>
                                <textarea name="answer_text"
                                          rows="6"
                                          class="w-full px-4 py-3 border-2 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-lg"
                                          placeholder="Masukkan jawaban Anda di sini..."
                                          onchange="handleAnswerChange(this)"><?php echo e($existingAnswers[$currentQuestion->id] ?? ''); ?></textarea>
                            </div>
                        <?php endif; ?>

                        <!-- Navigation Buttons -->
                        <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
                            <div class="flex items-center space-x-3">
                                <?php if($currentQuestionIndex > 1): ?>
                                    <button type="button" onclick="navigateToQuestion(<?php echo e($currentQuestionIndex - 1); ?>)"
                                            class="flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-sm hover:shadow-md">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                        </svg>
                                        Sebelumnya
                                    </button>
                                <?php endif; ?>
                                <?php if($currentQuestionIndex < $questions->count()): ?>
                                    <button type="button" onclick="navigateToQuestion(<?php echo e($currentQuestionIndex + 1); ?>)"
                                            class="flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg">
                                        Selanjutnya
                                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                <?php endif; ?>
                            </div>

                            <div class="text-sm text-gray-500 font-medium bg-gray-50 px-4 py-2 rounded-lg">
                                Soal <?php echo e($currentQuestionIndex); ?> dari <?php echo e($questions->count()); ?>

                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar - Navigation & Progress -->
                <div class="xl:col-span-1">
                    <div class="bg-white rounded-xl shadow-lg p-6 sticky top-28">
                        <h3 class="text-lg font-bold text-gray-900 mb-6">Navigasi Soal</h3>

                        <!-- Progress Summary -->
                        <div class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
                            <?php
                                $answeredCount = collect($existingAnswers)->filter(function($answer) {
                                    return $answer !== null && $answer !== '';
                                })->count();
                                $progressPercentage = $questions->count() > 0 ? ($answeredCount / $questions->count()) * 100 : 0;
                            ?>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-blue-600 mb-1" id="answeredCount"><?php echo e($answeredCount); ?></div>
                                <div class="text-sm text-gray-600">dari <?php echo e($questions->count()); ?> soal</div>
                                <div class="mt-2 text-xs text-gray-500"><?php echo e(number_format($progressPercentage, 1)); ?>% selesai</div>
                            </div>
                        </div>

                        <!-- Question Grid -->
                        <div class="grid grid-cols-5 gap-2 mb-6">
                            <?php $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isAnswered = isset($existingAnswers[$question->id]) &&
                                                 ($existingAnswers[$question->id] !== null &&
                                                  $existingAnswers[$question->id] !== '');
                                    $isCurrent = ($index + 1) == $currentQuestionIndex;
                                ?>
                                <button type="button" 
                                        onclick="navigateToQuestion(<?php echo e($index + 1); ?>)"
                                        data-question="<?php echo e($index + 1); ?>"
                                        class="question-nav-btn <?php if($isCurrent): ?> current <?php elseif($isAnswered): ?> answered <?php else: ?> unanswered <?php endif; ?>">
                                    <?php echo e($index + 1); ?>

                                </button>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Legend -->
                        <div class="space-y-3 text-sm mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-gray-200 border-2 border-gray-300 rounded"></div>
                                <span class="text-gray-600">Belum dijawab</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-green-500 border-2 border-green-500 rounded"></div>
                                <span class="text-gray-600">Sudah dijawab</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-blue-500 border-2 border-blue-500 rounded"></div>
                                <span class="text-gray-600">Sedang dilihat</span>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <button type="button" onclick="submitExam()" 
                                class="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white py-4 rounded-xl font-semibold shadow-lg transition-all duration-200 transform hover:scale-105">
                            <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Selesai Ujian
                        </button>
                        <p class="text-xs text-gray-500 mt-3 text-center">
                            Pastikan semua soal sudah dijawab sebelum menyelesaikan ujian
                        </p>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Exam configuration
const examConfig = {
    examId: '<?php echo e($exam->id); ?>',
    currentQuestionId: '<?php echo e($currentQuestion->id); ?>',
    currentQuestionIndex: <?php echo e($currentQuestionIndex); ?>,
    totalQuestions: <?php echo e($questions->count()); ?>,
    timeRemaining: <?php echo e($timeRemaining); ?>,
    answeredCount: <?php echo e($answeredCount); ?>

};

// Timer functionality
let timeRemaining = examConfig.timeRemaining;
let timerInterval;

function startTimer() {
    timerInterval = setInterval(updateTimer, 1000);
}

function updateTimer() {
    if (timeRemaining <= 0) {
        clearInterval(timerInterval);
        alert('Waktu ujian telah habis! Ujian akan otomatis diselesaikan.');
        document.getElementById('examForm').submit();
        return;
    }

    const timerElement = document.getElementById('timer');
    if (timerElement) {
        timerElement.textContent = ExamUtils.formatTime(timeRemaining);

        // Add warning styles when time is running low
        const timerContainer = timerElement.closest('.bg-gradient-to-r');
        if (timeRemaining <= 300) { // 5 minutes
            timerContainer.className = 'bg-gradient-to-r from-red-100 to-red-50 border-2 border-red-300 rounded-xl px-4 py-3 shadow-sm timer-warning';
            timerElement.className = 'font-mono text-lg font-bold text-red-800';
        } else if (timeRemaining <= 600) { // 10 minutes
            timerContainer.className = 'bg-gradient-to-r from-yellow-50 to-orange-50 border-2 border-yellow-300 rounded-xl px-4 py-3 shadow-sm';
            timerElement.className = 'font-mono text-lg font-bold text-yellow-800';
        }
    }

    timeRemaining--;
}

// Auto-save functionality
function handleAnswerChange(element) {
    const questionId = examConfig.currentQuestionId;
    let data = {};

    if (element.type === 'radio') {
        data.selected_option_id = element.value;

        // Update UI to show selection with smooth animation
        document.querySelectorAll('.option-card').forEach(card => {
            card.classList.remove('selected');
        });

        const selectedCard = element.closest('.option-card');
        selectedCard.classList.add('selected');

        // Add a subtle success animation
        selectedCard.style.transform = 'scale(1.02)';
        setTimeout(() => {
            selectedCard.style.transform = '';
        }, 200);

    } else if (element.tagName === 'TEXTAREA') {
        data.answer_text = element.value.trim();
    }

    // Auto-save the answer with debouncing for textarea
    clearTimeout(window.autoSaveTimeout);
    window.autoSaveTimeout = setTimeout(() => {
        ExamUtils.autoSave(examConfig.examId, questionId, data)
            .then(() => {
                updateProgress();
                updateQuestionNavigation();

                // Show subtle success feedback
                if (element.type === 'radio') {
                    const selectedCard = element.closest('.option-card');
                    const checkmark = selectedCard.querySelector('.success-checkmark');
                    if (!checkmark) {
                        const check = document.createElement('div');
                        check.className = 'success-checkmark absolute top-2 right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center opacity-0 transition-opacity duration-300';
                        check.innerHTML = '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
                        selectedCard.appendChild(check);

                        setTimeout(() => check.style.opacity = '1', 50);
                        setTimeout(() => {
                            check.style.opacity = '0';
                            setTimeout(() => check.remove(), 300);
                        }, 1500);
                    }
                }
            })
            .catch(error => {
                console.error('Failed to save answer:', error);
                // Show error feedback
                const errorToast = document.createElement('div');
                errorToast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
                errorToast.textContent = 'Gagal menyimpan jawaban. Coba lagi.';
                document.body.appendChild(errorToast);
                setTimeout(() => errorToast.remove(), 3000);
            });
    }, element.tagName === 'TEXTAREA' ? 1000 : 100); // Debounce textarea input
}

// Navigation functions
function navigateToQuestion(questionNumber) {
    if (questionNumber < 1 || questionNumber > examConfig.totalQuestions) {
        return;
    }

    // Mark as internal navigation to prevent beforeunload warning
    if (window.markInternalNavigation) {
        window.markInternalNavigation();
    }

    // Save current answer before navigating
    const currentAnswer = getCurrentAnswer();
    if (currentAnswer) {
        ExamUtils.autoSave(examConfig.examId, examConfig.currentQuestionId, currentAnswer)
            .finally(() => {
                window.location.href = `<?php echo e(route('exams.take', $exam)); ?>?question=${questionNumber}`;
            });
    } else {
        window.location.href = `<?php echo e(route('exams.take', $exam)); ?>?question=${questionNumber}`;
    }
}

function getCurrentAnswer() {
    const selectedOption = document.querySelector('input[name="selected_option"]:checked');
    const answerText = document.querySelector('textarea[name="answer_text"]');

    let data = {};

    if (selectedOption) {
        data.selected_option_id = selectedOption.value;
    }

    if (answerText && answerText.value.trim()) {
        data.answer_text = answerText.value.trim();
    }

    return Object.keys(data).length > 0 ? data : null;
}

// Progress tracking
function updateProgress() {
    // Count answered questions
    let answeredCount = 0;
    document.querySelectorAll('.question-nav-btn.answered').forEach(() => answeredCount++);

    // Check if current question is answered
    const currentAnswer = getCurrentAnswer();
    if (currentAnswer) {
        answeredCount++;
    }

    const progressPercentage = ExamUtils.calculateProgress(answeredCount, examConfig.totalQuestions);

    // Update progress bar
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressPercentage');
    const answeredCountElement = document.getElementById('answeredCount');

    if (progressBar) progressBar.style.width = progressPercentage + '%';
    if (progressText) progressText.textContent = progressPercentage + '%';
    if (answeredCountElement) answeredCountElement.textContent = answeredCount;
}

function updateQuestionNavigation() {
    const currentAnswer = getCurrentAnswer();
    const isAnswered = currentAnswer !== null;

    ExamUtils.updateQuestionStatus(examConfig.currentQuestionIndex, isAnswered);
}

// Submit exam
function submitExam() {
    // Save current answer first
    const currentAnswer = getCurrentAnswer();
    if (currentAnswer) {
        ExamUtils.autoSave(examConfig.examId, examConfig.currentQuestionId, currentAnswer);
    }

    // Count unanswered questions
    const unansweredCount = document.querySelectorAll('.question-nav-btn.unanswered').length;

    // Show confirmation dialog
    if (ExamUtils.confirmSubmit(unansweredCount)) {
        // Mark as internal navigation to prevent beforeunload warning
        if (window.markInternalNavigation) {
            window.markInternalNavigation();
        }

        // Disable submit buttons to prevent double submission
        const submitButtons = document.querySelectorAll('button[onclick="submitExam()"]');
        submitButtons.forEach(btn => {
            btn.disabled = true;
            btn.innerHTML = '<svg class="w-5 h-5 mr-2 inline animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Menyimpan...';
        });

        // Submit form after a short delay
        setTimeout(() => {
            document.getElementById('examForm').submit();
        }, 1000);
    }
}

// Initialize exam interface
document.addEventListener('DOMContentLoaded', function() {
    console.log('Professional Exam Interface Loaded');

    // Add smooth page transition
    setTimeout(() => {
        document.getElementById('examContent').classList.add('loaded');
    }, 100);

    // Start timer
    startTimer();

    // Update initial progress
    updateProgress();
    updateQuestionNavigation();

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Arrow keys for navigation
        if (e.key === 'ArrowLeft' && examConfig.currentQuestionIndex > 1) {
            e.preventDefault();
            navigateToQuestion(examConfig.currentQuestionIndex - 1);
        } else if (e.key === 'ArrowRight' && examConfig.currentQuestionIndex < examConfig.totalQuestions) {
            e.preventDefault();
            navigateToQuestion(examConfig.currentQuestionIndex + 1);
        }

        // Number keys for option selection (1-4)
        if (e.key >= '1' && e.key <= '4') {
            const optionIndex = parseInt(e.key) - 1;
            const radioButtons = document.querySelectorAll('input[name="selected_option"]');
            if (radioButtons[optionIndex]) {
                radioButtons[optionIndex].checked = true;
                handleAnswerChange(radioButtons[optionIndex]);
            }
        }
    });

    // Auto-save on page unload
    window.addEventListener('beforeunload', function() {
        const currentAnswer = getCurrentAnswer();
        if (currentAnswer) {
            // Use sendBeacon for reliable auto-save on page unload
            const data = new FormData();
            data.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
            data.append('question_id', examConfig.currentQuestionId);

            if (currentAnswer.selected_option_id) {
                data.append('selected_option_id', currentAnswer.selected_option_id);
            }
            if (currentAnswer.answer_text) {
                data.append('answer_text', currentAnswer.answer_text);
            }

            navigator.sendBeacon(`/exams/${examConfig.examId}/auto-save`, data);
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.exam-v2', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\ngambiskuynew\resources\views/exams-v2/take.blade.php ENDPATH**/ ?>